import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getFrontBoxSchema, GetFrontBoxParams } from '@/lib/validations/config';
import { withValidation } from '@/lib/middleware/errorHandler';
import { successResponse } from '@/lib/utils/apiResponse';
import { GetFrontBoxResponse } from '@/types/config';
import { BoxStatus } from '@/types/box';

const handler = withValidation<GetFrontBoxParams>(
  getFrontBoxSchema,
  async (request: NextRequest, validatedData: GetFrontBoxParams) => {
    try {
      // 查询 frontBox 配置
      const config = await prisma.config.findUnique({
        where: {
          name: 'frontBox'
        }
      });

      // 如果没有配置，返回空数组
      if (!config || !config.value) {
        const response: GetFrontBoxResponse = {
          boxes: []
        };
        return successResponse(response, '获取前端盒型数据成功');
      }

      // 解析配置值获取盒型ID数组
      const configValue = config.value as { boxIds: number[] };
      const boxIds = configValue.boxIds || [];

      if (boxIds.length === 0) {
        const response: GetFrontBoxResponse = {
          boxes: []
        };
        return successResponse(response, '获取前端盒型数据成功');
      }

      // 根据ID数组查询盒型数据，只返回状态为"启用"的盒型
      const boxes = await prisma.box.findMany({
        where: {
          id: {
            in: boxIds
          },
          status: BoxStatus.PUBLISHED, // 只返回已发布的盒型
          isDel: false
        },
        select: {
          id: true,
          name: true,
          status: true,
          description: true,
          images: {
            select: {
              id: true,
              name: true,
              mimeType: true,
              sortOrder: true
            },
            orderBy: {
              sortOrder: 'asc'
            }
          }
        }
      });

      // 按照配置中的顺序排序盒型
      const sortedBoxes = boxIds
        .map(id => boxes.find(box => box.id === id))
        .filter(box => box !== undefined); // 过滤掉不存在或已删除的盒型

      const response: GetFrontBoxResponse = {
        boxes: sortedBoxes
      };

      return successResponse(response, '获取前端盒型数据成功');
    } catch (error) {
      console.error('获取前端盒型数据失败:', error);
      throw error;
    }
  }
);

export const POST = handler;
