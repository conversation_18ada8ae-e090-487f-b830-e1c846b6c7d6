'use client';

import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Button, 
  Space, 
  Input, 
  Select, 
  DatePicker, 
  Tag, 
  Modal, 
  message,
  Popconfirm,
  Card,
  Row,
  Col,
  Typography
} from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { 
  UserListItem, 
  QueryUserParams, 
  UserRole, 
  UserState,
  USER_ROLE_LABELS,
  USER_STATE_LABELS,
  USER_ROLE_OPTIONS,
  USER_STATE_OPTIONS
} from '@/types/user';
import { getUserList, deleteUser, updateUserState } from '@/services/userApi';
import UserFormModal from './components/UserFormModal';

const { Title } = Typography;
const { RangePicker } = DatePicker;

export default function UsersPage() {
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<UserListItem[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchParams, setSearchParams] = useState<QueryUserParams>({
    page: 1,
    pageSize: 10
  });
  const [formModalVisible, setFormModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<UserListItem | null>(null);

  // 获取用户列表
  const fetchUsers = async (params?: Partial<QueryUserParams>) => {
    setLoading(true);
    try {
      const queryParams = { ...searchParams, ...params };
      const result = await getUserList(queryParams);
      
      if (result.success && result.data) {
        setUsers(result.data.list);
        setTotal(result.data.pagination.total);
        setCurrentPage(result.data.pagination.page);
        setPageSize(result.data.pagination.pageSize);
      } else {
        message.error(result.error?.message || '获取用户列表失败');
      }
    } catch (error) {
      message.error('获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchUsers();
  }, []);

  // 搜索处理
  const handleSearch = () => {
    const params = { ...searchParams, page: 1 };
    setSearchParams(params);
    fetchUsers(params);
  };

  // 重置搜索
  const handleReset = () => {
    const params: QueryUserParams = { page: 1, pageSize };
    setSearchParams(params);
    fetchUsers(params);
  };

  // 分页处理
  const handleTableChange = (page: number, size: number) => {
    const params = { ...searchParams, page, pageSize: size };
    setSearchParams(params);
    fetchUsers(params);
  };

  // 删除用户
  const handleDelete = async (id: number) => {
    try {
      const result = await deleteUser({ id });
      if (result.success) {
        message.success('删除成功');
        fetchUsers();
      } else {
        message.error(result.error?.message || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 更新用户状态
  const handleStateChange = async (id: number, state: UserState) => {
    try {
      const result = await updateUserState({ id, state });
      if (result.success) {
        message.success('状态更新成功');
        fetchUsers();
      } else {
        message.error(result.error?.message || '状态更新失败');
      }
    } catch (error) {
      message.error('状态更新失败');
    }
  };

  // 打开创建用户弹窗
  const handleCreate = () => {
    setEditingUser(null);
    setFormModalVisible(true);
  };

  // 打开编辑用户弹窗
  const handleEdit = (user: UserListItem) => {
    setEditingUser(user);
    setFormModalVisible(true);
  };

  // 表单提交成功回调
  const handleFormSuccess = () => {
    setFormModalVisible(false);
    setEditingUser(null);
    fetchUsers();
  };

  // 表格列定义
  const columns: ColumnsType<UserListItem> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
      width: 130,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 180,
      render: (email: string | null) => email || '-',
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 100,
      render: (role: UserRole) => (
        <Tag color={role === UserRole.ADMIN ? 'red' : role === UserRole.INTERNAL_USER ? 'blue' : 'default'}>
          {USER_ROLE_LABELS[role]}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'state',
      key: 'state',
      width: 80,
      render: (state: UserState) => (
        <Tag color={state === UserState.ENABLED ? 'green' : 'red'}>
          {USER_STATE_LABELS[state]}
        </Tag>
      ),
    },
    {
      title: '到期时间',
      dataIndex: 'expiresAt',
      key: 'expiresAt',
      width: 120,
      render: (expiresAt: Date | null) => 
        expiresAt ? dayjs(expiresAt).format('YYYY-MM-DD') : '-',
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginAt',
      key: 'lastLoginAt',
      width: 150,
      render: (lastLoginAt: Date | null) => 
        lastLoginAt ? dayjs(lastLoginAt).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (createdAt: Date) => dayjs(createdAt).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => handleStateChange(
              record.id, 
              record.state === UserState.ENABLED ? UserState.DISABLED : UserState.ENABLED
            )}
          >
            {record.state === UserState.ENABLED ? '禁用' : '启用'}
          </Button>
          <Popconfirm
            title="确定要删除这个用户吗？"
            description="删除后无法恢复，请谨慎操作。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
            icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '16px' }}>
          <Title level={4}>用户管理</Title>
        </div>

        {/* 搜索区域 */}
        <Card size="small" style={{ marginBottom: '16px' }}>
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Input
                placeholder="搜索姓名、手机号、邮箱"
                value={searchParams.keyword}
                onChange={(e) => setSearchParams({ ...searchParams, keyword: e.target.value })}
                onPressEnter={handleSearch}
              />
            </Col>
            <Col span={4}>
              <Select
                placeholder="选择角色"
                value={searchParams.role}
                onChange={(value) => setSearchParams({ ...searchParams, role: value })}
                allowClear
                style={{ width: '100%' }}
              >
                {USER_ROLE_OPTIONS.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Col>
            <Col span={4}>
              <Select
                placeholder="选择状态"
                value={searchParams.state}
                onChange={(value) => setSearchParams({ ...searchParams, state: value })}
                allowClear
                style={{ width: '100%' }}
              >
                {USER_STATE_OPTIONS.map(option => (
                  <Select.Option key={option.value} value={option.value}>
                    {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <RangePicker
                value={searchParams.startTime && searchParams.endTime ? [
                  dayjs(searchParams.startTime),
                  dayjs(searchParams.endTime)
                ] : null}
                onChange={(dates) => {
                  if (dates) {
                    setSearchParams({
                      ...searchParams,
                      startTime: dates[0]?.toISOString(),
                      endTime: dates[1]?.toISOString()
                    });
                  } else {
                    setSearchParams({
                      ...searchParams,
                      startTime: undefined,
                      endTime: undefined
                    });
                  }
                }}
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={4}>
              <Space>
                <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
                <Button icon={<ReloadOutlined />} onClick={() => fetchUsers()}>
                  刷新
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 操作按钮 */}
        <div style={{ marginBottom: '16px' }}>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            新建用户
          </Button>
        </div>

        {/* 用户表格 */}
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: handleTableChange,
            onShowSizeChange: handleTableChange,
          }}
          scroll={{ x: 1200 }}
        />

        {/* 用户表单弹窗 */}
        <UserFormModal
          visible={formModalVisible}
          user={editingUser}
          onSuccess={handleFormSuccess}
          onCancel={() => {
            setFormModalVisible(false);
            setEditingUser(null);
          }}
        />
      </Card>
    </div>
  );
}
