// 配置相关 API 服务，封装所有配置相关的后端交互
import { resultApi } from '@/lib/utils/request';
import {
  GetFrontBoxResponse,
  GetFrontBoxConfigResponse,
  UpdateFrontBoxConfigParams,
  GetProfitTaxConfigResponse,
  UpdateProfitTaxConfigParams,
  Config
} from '@/types/config';
import { Box, PublicBoxListParams, PublicBoxListResponse } from '@/types/box';
import { Result, PaginatedData } from '@/types/common';

/**
 * 配置相关 API 服务 - 使用Result类型
 */
export const configApi = {
  /**
   * 获取前端盒型数据（公开接口）
   */
  getFrontBox: (): Promise<Result<GetFrontBoxResponse>> => {
    return resultApi.post<GetFrontBoxResponse>('/api/v1/box/getFrontBox', {});
  },

  /**
   * 获取前端盒型配置（管理后台）
   */
  getFrontBoxConfig: (): Promise<Result<GetFrontBoxConfigResponse>> => {
    return resultApi.post<GetFrontBoxConfigResponse>('/api/v1/admin/config/getFrontBoxConfig', {});
  },

  /**
   * 更新前端盒型配置（管理后台）
   */
  updateFrontBoxConfig: (params: UpdateFrontBoxConfigParams): Promise<Result<Config>> => {
    return resultApi.post<Config>('/api/v1/admin/config/updateFrontBoxConfig', params);
  },

  /**
   * 获取可用盒型列表（管理后台）
   */
  getAvailableBoxes: (): Promise<Result<Box[]>> => {
    return resultApi.post<Box[]>('/api/v1/admin/config/getAvailableBoxes', {});
  },

  /**
   * 获取利润税率配置（管理后台）
   */
  getProfitTaxConfig: (): Promise<Result<GetProfitTaxConfigResponse>> => {
    return resultApi.post<GetProfitTaxConfigResponse>('/api/v1/admin/config/getProfitTaxConfig', {});
  },

  /**
   * 更新利润税率配置（管理后台）
   */
  updateProfitTaxConfig: (params: UpdateProfitTaxConfigParams): Promise<Result<Config>> => {
    return resultApi.post<Config>('/api/v1/admin/config/updateProfitTaxConfig', params);
  },
};
