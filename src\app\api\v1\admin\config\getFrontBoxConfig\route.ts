import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getFrontBoxSchema, GetFrontBoxParams } from '@/lib/validations/config';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { GetFrontBoxConfigResponse } from '@/types/config';

const handler = withValidation<GetFrontBoxParams>(
  getFrontBoxSchema,
  async (request: AuthenticatedRequest, validatedData: GetFrontBoxParams) => {
    try {
      // 查询 frontBox 配置
      const config = await prisma.config.findUnique({
        where: {
          name: 'frontBox'
        }
      });

      let frontBoxConfig = null;
      if (config && config.value) {
        frontBoxConfig = config.value as { boxIds: number[] };
      }

      const response: GetFrontBoxConfigResponse = {
        config: frontBoxConfig
      };

      return successResponse(response, '获取前端盒型配置成功');
    } catch (error) {
      console.error('获取前端盒型配置失败:', error);
      throw error;
    }
  }
);

export const POST = withInternalAuth(handler);
