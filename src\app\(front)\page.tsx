'use client';

import React from 'react';
import { Layout, ConfigProvider } from 'antd';
import Header from './components/Header';
import Hero from './components/Hero';
import Footer from './components/Footer';
import Interval from './components/Interval';
import BoxShowcase from './components/BoxShowcase';

import './styles.css';

/**
 * 前台首页
 */
export default function FrontPage() {
  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#FF422D',        // 主色（会影响选中颜色）
          colorPrimaryHover: '#FF6655',   // 主色悬浮颜色
          colorPrimaryActive: '#CC3B26',  // 主色点击颜色
          controlItemBgActive: '#FF422D', // 控件激活时背景（Menu 选中背景）
        },
        components: {
          Menu: {
            itemSelectedBg: '#FF422D',       // 选中背景色
            itemSelectedColor: '#fff',       // 选中字体色
          },
        },
      }}
    >
      <Layout style={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
        <Header />
        <Hero />
        <Interval />
        <BoxShowcase />
        <Footer />
      </Layout>
    </ConfigProvider>
  );
}
