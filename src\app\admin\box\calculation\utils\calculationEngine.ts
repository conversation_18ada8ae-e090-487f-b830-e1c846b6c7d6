import { perfLog } from '@/lib/utils/perfLog';
import {
  PackagingConfig,
  MaterialConfig,
  ProcessConfig,
  AccessoryConfig,
  ProcessingFeeConfig,
  FormulaConfig,
  CalculationState,
  QuotationDetail,
  CalculationEngine
} from '../types/calculation';
import { configApi } from '@/services/configApi';
import { ProfitTaxConfig } from '@/types/config';

/**
 * 盒型计算引擎实现
 */
export class BoxCalculationEngine implements CalculationEngine {
  /**
   * 计算材料费用
   */
  async calculateMaterialCost(
    materialConfig: MaterialConfig,
    packagingConfig: PackagingConfig
  ): Promise<number> {
    try {
      perfLog.debug('使用ImpositionLogicDisplay计算的材料费用:', packagingConfig.materialCost, materialConfig);
      return packagingConfig.materialCost || 0;
    } catch (error) {
      perfLog.error('材料费用计算错误:', error);
      throw new Error('材料费用计算失败');
    }
  }

  /**
   * 计算工艺费用
   */
  async calculateProcessCost(
    processConfig: ProcessConfig,
    _packagingConfig: PackagingConfig
  ): Promise<number> {
    let totalCost = 0;

    try {
      // 优先使用新的部件分组工艺配置
      if (processConfig.partGroupProcessConfigs) {
        Object.values(processConfig.partGroupProcessConfigs).forEach(groupConfig => {
          totalCost += groupConfig.groupProcessCost || 0;
        });
      } else {
        // 兼容旧的工艺配置结构
        const processTypes = [
          'printing', 'surfaceProcess', 'silkScreen', 'hotStamping',
          'laminating', 'embossing', 'corrugated', 'dieCutting', 'dieCuttingPlateFee'
        ];

        processTypes.forEach(processType => {
          const processItems = processConfig[processType as keyof ProcessConfig];
          if (processItems && Array.isArray(processItems)) {
            processItems.forEach(item => {
              totalCost += item.totalPrice || 0;
            });
          }
        });
      }

      return totalCost;
    } catch (error) {
      perfLog.error('工艺费用计算错误:', error);
      throw new Error('工艺费用计算失败');
    }
  }

  /**
   * 计算配件费用
   */
  async calculateAccessoryCost(accessoryConfig: AccessoryConfig): Promise<number> {
    let totalCost = 0;
    
    try {
      // 计算普通配件费用
      if (accessoryConfig.accessories) {
        for (const accessory of accessoryConfig.accessories) {
          totalCost += accessory.totalPrice;
        }
      }
      
      // 计算礼盒配件费用
      if (accessoryConfig.giftBoxAccessories) {
        for (const giftBoxAccessory of accessoryConfig.giftBoxAccessories) {
          totalCost += giftBoxAccessory.totalPrice;
        }
      }
      
      return totalCost;
    } catch (error) {
      perfLog.error('配件费用计算错误:', error);
      throw new Error('配件费用计算失败');
    }
  }

  /**
   * 计算加工费费用
   */
  async calculateProcessingFeeCost(processingFeeConfig: ProcessingFeeConfig): Promise<number> {
    let totalCost = 0;

    try {
      // 计算自定义加工费费用
      if (processingFeeConfig.customFees) {
        for (const fee of processingFeeConfig.customFees) {
          totalCost += fee.totalPrice || 0;
        }
      }

      // 计算固定选项加工费费用
      if (processingFeeConfig.fixedFees) {
        for (const fee of processingFeeConfig.fixedFees) {
          totalCost += fee.totalPrice || 0;
        }
      }

      return totalCost;
    } catch (error) {
      perfLog.error('加工费费用计算错误:', error);
      throw new Error('加工费费用计算失败');
    }
  }

  /**
   * 计算公式费用
   * 注意：公式费用的详细计算在QuotationStep组件中进行，这里直接使用已计算的结果
   */
  async calculateFormulaCost(
    formulaConfig: FormulaConfig,
    _state: CalculationState
  ): Promise<number> {
    try {
      // 如果formulaConfig中已经有计算好的费用，直接使用
      if (typeof formulaConfig.formulaCost === 'number') {
        perfLog.debug('使用已计算的公式费用:', formulaConfig.formulaCost);
        return formulaConfig.formulaCost;
      }

      // 如果没有预计算的费用，进行简单计算（仅起步金额）
      let totalCost = 0;

      if (formulaConfig.formulas) {
        for (const formula of formulaConfig.formulas) {
          totalCost += formula.initialAmount || 0;
        }
      }

      // 计算附加费用
      if (formulaConfig.additionalFees) {
        for (const fee of formulaConfig.additionalFees) {
          totalCost += fee.amount;
        }
      }

      perfLog.debug('计算引擎公式费用（仅起步金额）:', totalCost);
      return totalCost;
    } catch (error) {
      perfLog.error('公式费用计算错误:', error);
      return 0; // 返回0而不是抛出错误，避免影响整体计算
    }
  }

  /**
   * 生成报价明细
   */
  async generateQuotation(state: CalculationState, profitTaxConfig?: ProfitTaxConfig): Promise<QuotationDetail> {
    try {
      const materialCost = await this.calculateMaterialCost(
        state.materialConfig,
        state.packagingConfig
      );

      const processCost = await this.calculateProcessCost(
        state.processConfig,
        state.packagingConfig
      );

      const accessoryCost = await this.calculateAccessoryCost(state.accessoryConfig);

      const processingFeeCost = await this.calculateProcessingFeeCost(state.processingFeeConfig);

      const formulaCost = await this.calculateFormulaCost(
        state.formulaConfig,
        state
      );

      // 计算基础成本总额
      const baseCost = materialCost + processCost + accessoryCost + processingFeeCost + formulaCost;

      // 使用传入的利润税率配置，如果没有则使用默认值
      const finalProfitTaxConfig = profitTaxConfig || {
        profitRate: 0.2,  // 默认20%
        taxRate: 0.06     // 默认6%
      };

      // 应用利润率和税率计算最终报价
      // 最终报价 = 基础成本 × (1 + 利润率) × (1 + 税率)
      const totalCost = baseCost * (1 + finalProfitTaxConfig.profitRate) * (1 + finalProfitTaxConfig.taxRate);

      perfLog.debug('报价计算详情:', {
        baseCost,
        profitRate: finalProfitTaxConfig.profitRate,
        taxRate: finalProfitTaxConfig.taxRate,
        totalCost
      });

      return {
        basicInfo: state.basicInfo,
        materialCost,
        processCost,
        accessoryCost,
        processingFeeCost,
        formulaCost,
        totalCost,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
    } catch (error) {
      perfLog.error('报价生成错误:', error);
      throw new Error('报价生成失败');
    }
  }

  /**
   * 计算纸张费用
   */
  private calculatePaperCost(paper: any, packagingConfig: PackagingConfig): number {
    // 基础计算逻辑，实际应根据具体纸张类型和用量计算
    const basePrice = paper.unitPrice * paper.quantity;
    // 从配置中获取面纸损耗率，如果没有配置则使用默认值10%
    const wasteRate = (packagingConfig.paperWasteRate ?? 10) / 100;
    return basePrice * (1 + wasteRate);
  }

  /**
   * 计算特种纸费用
   */
  private calculateSpecialPaperCost(specialPaper: any, packagingConfig: PackagingConfig): number {
    const basePrice = specialPaper.unitPrice * specialPaper.quantity;
    // 从配置中获取特种纸损耗率，如果没有配置则使用默认值5%
    const wasteRate = (packagingConfig.specialPaperWasteRate ?? 5) / 100;
    return basePrice * (1 + wasteRate);
  }

  /**
   * 计算灰板费用
   */
  private calculateGreyBoardCost(greyBoard: any, packagingConfig: PackagingConfig): number {
    const basePrice = greyBoard.unitPrice * greyBoard.quantity;
    // 从配置中获取灰板损耗率，如果没有配置则使用默认值8%
    const wasteRate = (packagingConfig.greyBoardWasteRate ?? 8) / 100;
    return basePrice * (1 + wasteRate);
  }

  /**
   * 计算瓦楞材料费用
   */
  private calculateCorrugatedCost(corrugated: any, packagingConfig: PackagingConfig): number {
    const basePrice = corrugated.unitPrice * corrugated.quantity;
    // 从配置中获取瓦楞材料损耗率，如果没有配置则使用默认值7%
    const wasteRate = (packagingConfig.corrugatedWasteRate ?? 7) / 100;
    return basePrice * (1 + wasteRate);
  }
}

// 导出默认实例
export const calculationEngine = new BoxCalculationEngine(); 