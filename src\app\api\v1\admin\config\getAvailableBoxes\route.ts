import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { BoxStatus } from '@/types/box';
import { z } from 'zod';

// 空参数校验
const getAvailableBoxesSchema = z.object({});

const handler = withValidation(
  getAvailableBoxesSchema,
  async (request: AuthenticatedRequest, validatedData: any) => {
    try {
      // 查询所有已发布且未删除的盒型
      const boxes = await prisma.box.findMany({
        where: {
          status: BoxStatus.PUBLISHED,
          isDel: false
        },
        select: {
          id: true,
          name: true,
          description: true,
          images: {
            select: {
              id: true,
              name: true,
              mimeType: true,
              sortOrder: true
            },
            orderBy: {
              sortOrder: 'asc'
            },
            take: 1 // 只取第一张图片作为缩略图
          }
        },
        orderBy: [
          { updatedAt: 'desc' },
          { id: 'desc' }
        ]
      });

      return successResponse(boxes, '获取可用盒型列表成功');
    } catch (error) {
      console.error('获取可用盒型列表失败:', error);
      throw error;
    }
  }
);

export const POST = withInternalAuth(handler);
