import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { getDashboardStatsSchema } from '@/lib/validations/admin/dashboard';
import { BoxStatus } from '@/types/box';
import { DashboardStats } from '@/types/dashboard';

const handler = withValidation(
  getDashboardStatsSchema,
  async (request: AuthenticatedRequest, validatedData: any) => {
    try {
      // 并行查询所有统计数据
      const [
        totalBoxes,
        publishedBoxes,
        draftBoxes,
        totalUsers,
        earliestRecord
      ] = await Promise.all([
        // 盒型总数（未删除）
        prisma.box.count({
          where: {
            isDel: false
          }
        }),
        
        // 已发布盒型数量
        prisma.box.count({
          where: {
            isDel: false,
            status: BoxStatus.PUBLISHED
          }
        }),
        
        // 草稿盒型数量
        prisma.box.count({
          where: {
            isDel: false,
            status: BoxStatus.DRAFT
          }
        }),
        
        // 用户总数（未删除且启用）
        prisma.user.count({
          where: {
            isDel: false,
            state: 1 // 启用状态
          }
        }),
        
        // 查询最早的记录来计算系统运行天数
        prisma.user.findFirst({
          where: {
            isDel: false
          },
          select: {
            createdAt: true
          },
          orderBy: {
            createdAt: 'asc'
          }
        })
      ]);

      // 计算系统运行天数
      let systemRunningDays = 0;
      if (earliestRecord) {
        const now = new Date();
        const startDate = new Date(earliestRecord.createdAt);
        const diffTime = Math.abs(now.getTime() - startDate.getTime());
        systemRunningDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      }

      const stats: DashboardStats = {
        totalBoxes,
        publishedBoxes,
        draftBoxes,
        totalUsers,
        systemRunningDays
      };

      return successResponse(stats, '获取仪表盘统计数据成功');
    } catch (error) {
      console.error('获取仪表盘统计数据失败:', error);
      throw error;
    }
  }
);

export const POST = withInternalAuth(handler);
