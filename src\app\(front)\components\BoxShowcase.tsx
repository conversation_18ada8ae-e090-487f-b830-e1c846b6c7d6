'use client';

import React, { useState, useEffect } from 'react';
import { Row, Col, Spin, Alert, Typography, Empty } from 'antd';
import { GetFrontBoxResponse } from '@/types/config';
import { boxApi } from '@/services/boxApi';
import CustomBoxCard from './CustomBoxCard';

const { Title } = Typography;

interface BoxShowcaseProps {
  className?: string;
}

export default function BoxShowcase({ className = '' }: BoxShowcaseProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [boxes, setBoxes] = useState<GetFrontBoxResponse['boxes']>([]);

  // 获取盒型数据
  const fetchBoxes = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await boxApi.getFrontBox();
      
      if (result.success && result.data) {
        setBoxes(result.data.boxes);
      } else {
        setError(result.error?.message || '获取盒型数据失败');
      }
    } catch (err) {
      console.error('获取盒型数据失败:', err);
      setError('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBoxes();
  }, []);

  // 生成图片URL
  const getImageUrl = (imageId?: number) => {
    if (!imageId) return undefined;
    return `/api/v1/box/getImage?id=${imageId}`;
  };

  // 处理卡片点击
  const handleCardClick = (boxId: number) => {
    // TODO: 跳转到盒型详情页或计算页面
    console.log('点击盒型:', boxId);
  };

  if (loading) {
    return (
      <div className={`box-showcase ${className}`} style={{ padding: '40px 0', textAlign: 'center' }}>
        <Spin size="large">
          <div style={{ padding: '50px' }}>加载中...</div>
        </Spin>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`box-showcase ${className}`} style={{ padding: '40px 20px' }}>
        <Alert
          message="加载失败"
          description={error}
          type="error"
          showIcon
          style={{ maxWidth: 600, margin: '0 auto' }}
        />
      </div>
    );
  }

  if (boxes.length === 0) {
    return (
      <div className={`box-showcase ${className}`} style={{ padding: '40px 20px' }}>
        <Empty
          description="暂无盒型数据"
          style={{ margin: '0 auto' }}
        />
      </div>
    );
  }

  return (
    <div className={`box-showcase ${className}`} style={{ padding: '40px 20px', backgroundColor: '#f5f5f5'  }}>
      <div style={{ maxWidth: 1200, margin: '0 auto' }}>
        {/* 标题 */}
        <div style={{ textAlign: 'center', marginBottom: '40px' }}>
          <Title level={2} style={{ color: '#333', marginBottom: '8px' }}>
            精选盒型
          </Title>
          <p style={{ color: '#666', fontSize: '16px', margin: 0 }}>
            为您精心挑选的优质包装盒型，满足不同需求
          </p>
        </div>

        {/* 盒型网格 */}
        <Row gutter={[24, 24]} justify="center">
          {boxes.map((box) => {
            // 获取第一张图片
            const firstImage = box.images && box.images.length > 0 ? box.images[0] : null;
            const imageUrl = firstImage ? getImageUrl(firstImage.id) : undefined;

            return (
              <Col
                key={box.id}
                xs={24}
                sm={12}
                md={8}
                lg={6}
                xl={6}
                style={{ display: 'flex', justifyContent: 'center' }}
              >
                <CustomBoxCard
                  title={box.name}
                  subtitle={box.description || undefined}
                  imageUrl={imageUrl}
                  imageAlt={`${box.name}图片`}
                  onClick={() => handleCardClick(box.id)}
                  width={240}
                  height="auto"
                  imageHeight={160}
                  hoverable={true}
                />
              </Col>
            );
          })}
        </Row>
      </div>
    </div>
  );
}
