// 仪表盘相关类型定义

/**
 * 仪表盘统计数据接口
 */
export interface DashboardStats {
  /** 盒型总数 */
  totalBoxes: number;
  /** 已发布盒型数量 */
  publishedBoxes: number;
  /** 草稿盒型数量 */
  draftBoxes: number;
  /** 用户总数 */
  totalUsers: number;
  /** 系统运行天数 */
  systemRunningDays: number;
}

/**
 * 获取仪表盘统计数据的请求参数（暂时为空，预留扩展）
 */
export interface GetDashboardStatsParams {
  // 预留字段，可用于后续扩展（如时间范围筛选等）
}

/**
 * 获取仪表盘统计数据的响应类型
 */
export interface GetDashboardStatsResponse extends DashboardStats {}
