import { prisma } from '@/lib/prisma';
import { GetBoxImageParams, getBoxImageSchema } from '@/lib/validations/admin/box';
import { withQueryValidation, assert } from '@/lib/middleware/errorHandler';
import { AuthenticatedRequest, withOptionalAuth } from '@/lib/auth/middleware';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { successFileResponse } from '@/lib/utils/apiResponse';

const handler = withQueryValidation(
  getBoxImageSchema,
  async (request: AuthenticatedRequest, validatedQuery: GetBoxImageParams) => {
    const { id } = validatedQuery;

    const image = await prisma.boxImage.findUnique({
      where: { id },
      select: {
        imageData: true,
        mimeType: true,
        name: true,
      }
    });

    assert(!!image, ErrorCode.RESOURCE_NOT_FOUND, '图片不存在');

    return successFileResponse(Buffer.from(image.imageData), {
      'Content-Type': image.mimeType || 'image/jpeg',
      'Content-Disposition': `inline; filename="${encodeURIComponent(image.name)}"`,
      'Cache-Control': 'public, max-age=31536000', // 缓存一年
    });
  });

export const GET = withOptionalAuth(handler);
