import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateFrontBoxConfigSchema, UpdateFrontBoxConfigParams } from '@/lib/validations/config';
import { withValidation, assert } from '@/lib/middleware/errorHandler';
import { withInternalAuth, AuthenticatedRequest } from '@/lib/auth/middleware';
import { successResponse } from '@/lib/utils/apiResponse';
import { ErrorCode } from '@/lib/constants/errorCodes';
import { BoxStatus } from '@/types/box';

const handler = withValidation<UpdateFrontBoxConfigParams>(
  updateFrontBoxConfigSchema,
  async (request: AuthenticatedRequest, validatedData: UpdateFrontBoxConfigParams) => {
    const { boxIds } = validatedData;

    try {
      // 验证所有盒型ID是否存在且状态为已发布
      if (boxIds.length > 0) {
        const boxes = await prisma.box.findMany({
          where: {
            id: {
              in: boxIds
            },
            isDel: false
          },
          select: {
            id: true,
            status: true
          }
        });

        // 检查是否所有盒型都存在
        const existingBoxIds = boxes.map(box => box.id);
        const missingBoxIds = boxIds.filter(id => !existingBoxIds.includes(id));
        
        assert(
          missingBoxIds.length === 0,
          ErrorCode.RESOURCE_NOT_FOUND,
          `盒型不存在: ${missingBoxIds.join(', ')}`
        );

        // 检查是否所有盒型都是已发布状态
        const unpublishedBoxes = boxes.filter(box => box.status !== BoxStatus.PUBLISHED);
        
        assert(
          unpublishedBoxes.length === 0,
          ErrorCode.INVALID_PARAMETERS,
          `以下盒型未发布，无法添加到前端展示: ${unpublishedBoxes.map(box => box.id).join(', ')}`
        );
      }

      // 配置值
      const configValue = { boxIds };

      // 使用 upsert 操作，如果配置不存在则创建，存在则更新
      const config = await prisma.config.upsert({
        where: {
          name: 'frontBox'
        },
        update: {
          value: configValue
        },
        create: {
          name: 'frontBox',
          value: configValue
        }
      });

      return successResponse(config, '更新前端盒型配置成功');
    } catch (error) {
      console.error('更新前端盒型配置失败:', error);
      throw error;
    }
  }
);

export const POST = withInternalAuth(handler);
