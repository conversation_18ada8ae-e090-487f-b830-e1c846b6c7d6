'use client';

import React from 'react';
import { Result, Button, Space } from 'antd';
import { LockOutlined, HomeOutlined, LoginOutlined } from '@ant-design/icons';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { getUserRoleLabel } from '@/lib/auth/client';

/**
 * 403权限不足页面
 */
export default function AccessDeniedPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  
  // 获取原始请求的路径
  const redirectPath = searchParams.get('redirect') || '/admin';

  const handleGoHome = () => {
    router.push('/');
  };

  const handleLogin = () => {
    const loginUrl = `/login?redirect=${encodeURIComponent(redirectPath)}`;
    router.push(loginUrl);
  };

  const handleRetry = () => {
    // 刷新页面重新尝试
    window.location.reload();
  };

  // 根据用户状态显示不同的消息
  const getSubTitle = () => {
    if (!user) {
      return '您需要登录并具有管理员权限才能访问管理后台。';
    }

    return `抱歉，您的账户权限不足，无法访问管理后台。当前角色：${getUserRoleLabel(user.role)}`;
  };

  const getExtraActions = () => {
    const actions = [
      <Button 
        key="home"
        type="primary" 
        icon={<HomeOutlined />}
        onClick={handleGoHome}
        size="large"
      >
        返回首页
      </Button>
    ];

    if (!user) {
      // 未登录用户显示登录按钮
      actions.push(
        <Button 
          key="login"
          icon={<LoginOutlined />}
          onClick={handleLogin}
          size="large"
        >
          立即登录
        </Button>
      );
    } else {
      // 已登录用户显示重试按钮
      actions.push(
        <Button 
          key="retry"
          onClick={handleRetry}
          size="large"
        >
          刷新重试
        </Button>
      );
    }

    return actions;
  };

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      padding: '20px',
      background: '#f5f5f5'
    }}>
      <div style={{ 
        maxWidth: '600px', 
        width: '100%',
        background: '#fff',
        borderRadius: '8px',
        padding: '40px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
      }}>
        <Result
          status="403"
          title="访问受限"
          icon={<LockOutlined style={{ color: '#ff4d4f', fontSize: '72px' }} />}
          subTitle={
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div style={{ fontSize: '16px', color: '#595959', lineHeight: 1.5 }}>
                {getSubTitle()}
              </div>
              
              <div style={{ fontSize: '14px', color: '#8c8c8c' }}>
                管理后台仅限管理员和内部用户访问。如果您认为这是一个错误，请联系系统管理员。
              </div>
            </Space>
          }
          extra={
            <Space size="middle" wrap>
              {getExtraActions()}
            </Space>
          }
        />
      </div>
    </div>
  );
}
