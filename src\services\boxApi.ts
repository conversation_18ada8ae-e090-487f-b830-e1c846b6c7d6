// 配置相关 API 服务，封装所有配置相关的后端交互
import { resultApi } from '@/lib/utils/request';
import { PublicBoxListParams, PublicBoxListResponse, GetFrontBoxResponse } from '@/types/box';
import { Result, PaginatedData } from '@/types/common';

/**
 * 配置相关 API 服务 - 使用Result类型
 */
export const boxApi = {
  /**
   * 获取前端盒型数据（公开接口）
   */
  getFrontBox: (): Promise<Result<GetFrontBoxResponse>> => {
    return resultApi.post<GetFrontBoxResponse>('/api/v1/box/getFrontBox', {});
  },
  /**
   * 获取公开盒型列表（公开接口，支持分页）
   */
  getPublicBoxList: (params: PublicBoxListParams): Promise<Result<PaginatedData<PublicBoxListResponse['list'][0]>>> => {
    return resultApi.post<PaginatedData<PublicBoxListResponse['list'][0]>>('/api/v1/box/getList', params);
  }
};
