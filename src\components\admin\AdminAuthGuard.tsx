'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { useAuth } from '@/hooks/useAuth';
import { UserRole } from '@/types/user';
import { canAccessAdmin } from '@/lib/auth/client';

interface AdminAuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * 管理后台权限检查组件
 * 确保只有具有管理员权限的用户才能访问管理后台
 */
export default function AdminAuthGuard({ children, fallback }: AdminAuthGuardProps) {
  const { user, loading, isAuthenticated } = useAuth();
  const router = useRouter();
  const [showContent, setShowContent] = useState(false);

  useEffect(() => {
    if (loading) {
      return; // 仍在加载中，不做任何操作
    }

    if (!isAuthenticated || !user) {
      // 未登录，重定向到登录页面
      router.replace('/login?redirect=' + encodeURIComponent(window.location.pathname));
      return;
    }

    if (!canAccessAdmin(user.role)) {
      // 权限不足，重定向到首页
      router.replace('/');
      return;
    }

    // 权限验证通过，显示内容
    setShowContent(true);
  }, [user, loading, isAuthenticated, router]);

  // 显示加载状态
  if (loading) {
    return (
      fallback || (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          minHeight: '100vh',
          flexDirection: 'column'
        }}>
          <Spin 
            indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} 
            size="large" 
          />
          <div style={{ marginTop: 16, color: '#666' }}>
            正在验证权限...
          </div>
        </div>
      )
    );
  }

  // 未登录状态（正在重定向）
  if (!isAuthenticated || !user) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        flexDirection: 'column'
      }}>
        <Spin 
          indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} 
          size="large" 
        />
        <div style={{ marginTop: 16, color: '#666' }}>
          正在跳转到登录页面...
        </div>
      </div>
    );
  }

  // 权限不足的情况已在useEffect中处理（重定向到首页）
  // 这里不再需要显示权限不足页面

  // 权限验证通过，显示管理后台内容
  if (showContent) {
    return <>{children}</>;
  }

  // 默认显示加载状态
  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh'
    }}>
      <Spin size="large" />
    </div>
  );
}



/**
 * 检查用户是否有特定权限
 */
export function useAdminPermission() {
  const { user, loading, isAuthenticated } = useAuth();
  
  const hasPermission = React.useMemo(() => {
    if (loading || !isAuthenticated || !user) {
      return false;
    }
    return canAccessAdmin(user.role);
  }, [user, loading, isAuthenticated]);
  
  const isAdmin = React.useMemo(() => {
    if (loading || !isAuthenticated || !user) {
      return false;
    }
    return user.role === UserRole.ADMIN;
  }, [user, loading, isAuthenticated]);
  
  const isInternalUser = React.useMemo(() => {
    if (loading || !isAuthenticated || !user) {
      return false;
    }
    return user.role === UserRole.INTERNAL_USER;
  }, [user, loading, isAuthenticated]);
  
  return {
    hasPermission,
    isAdmin,
    isInternalUser,
    user,
    loading
  };
}
