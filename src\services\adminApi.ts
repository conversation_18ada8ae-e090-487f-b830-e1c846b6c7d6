// API服务，封装所有与后端的交互
import { resultApi } from '@/lib/utils/request';
import {
  Box,
  BoxCreateParams,
  BoxListParams,
  BoxUpdateParams
} from '@/types/box';
import {
  CustomFormula,
  CustomFormulaCreateParams,
  CustomFormulaListParams,
  CustomFormulaUpdateParams
} from '@/types/customFormula';
import {
  MaterialListParams,
  PaperCreateParams,
  PaperUpdateParams,
  PaperCuttingCreateParams,
  PaperCuttingUpdateParams,
  SpecialPaperCreateParams,
  SpecialPaperUpdateParams,
  GreyBoardCreateParams,
  GreyBoardUpdateParams,
  GreyBoardCuttingCreateParams,
  GreyBoardCuttingUpdateParams,
  Paper,
  PaperCutting,
  SpecialPaper,
  GreyBoard,
  GreyBoardCutting,
  StickerCreateParams,
  StickerUpdateParams,
  Sticker,
  SpecialMaterialCreateParams,
  SpecialMaterialUpdateParams,
  SpecialMaterial,
  AccessoryCreateParams,
  AccessoryUpdateParams,
  Accessory,
  GiftBoxAccessoryCreateParams,
  GiftBoxAccessoryUpdateParams,
  GiftBoxAccessory,
  BoxMaterialListParams,
  BoxMaterialCreateParams,
  BoxMaterialUpdateParams,
  BoxMaterial,
} from '@/types/material';
import {
  Printing,
  PrintingMachine,
  CreatePrintingRequest,
  UpdatePrintingRequest,
  CreatePrintingMachineRequest,
  UpdatePrintingMachineRequest,
  PrintingListParams,
  PrintingMachineListParams,
  SurfaceProcess,
  CreateSurfaceProcessRequest,
  UpdateSurfaceProcessRequest,
  SurfaceProcessListParams,
  SilkScreenProcess,
  CreateSilkScreenProcessRequest,
  UpdateSilkScreenProcessRequest,
  SilkScreenProcessListParams,
  CorrugatedProcess,
  CreateCorrugatedProcessRequest,
  UpdateCorrugatedProcessRequest,
  CorrugatedProcessListParams,
  CorrugatedRate,
  CreateCorrugatedRateRequest,
  UpdateCorrugatedRateRequest,
  CorrugatedRateListParams,
  LaminatingProcess,
  CreateLaminatingProcessRequest,
  UpdateLaminatingProcessRequest,
  LaminatingProcessListParams,
  HotStampingProcess,
  CreateHotStampingProcessRequest,
  UpdateHotStampingProcessRequest,
  HotStampingProcessListParams,
  HotStampingPlateFee,
  CreateHotStampingPlateFeeRequest,
  UpdateHotStampingPlateFeeRequest,
  HotStampingPlateFeeListParams,
  // 凹凸工艺模块类型
  TexturingProcess,
  CreateTexturingProcessRequest,
  UpdateTexturingProcessRequest,
  TexturingProcessListParams,
  EmbossingProcess,
  CreateEmbossingProcessRequest,
  UpdateEmbossingProcessRequest,
  EmbossingProcessListParams,
  HydraulicProcess,
  CreateHydraulicProcessRequest,
  UpdateHydraulicProcessRequest,
  HydraulicProcessListParams,
  // 加工费模块类型
  ProcessingFee,
  CreateProcessingFeeRequest,
  UpdateProcessingFeeRequest,
  ProcessingFeeListParams,
  ProcessingParams,
  UpdateProcessingParamsRequest,
  // 模切工艺模块类型
  DieCuttingProcess,
  CreateDieCuttingProcessRequest,
  UpdateDieCuttingProcessRequest,
  DieCuttingProcessListParams,
  DieCuttingPlateFee,
  CreateDieCuttingPlateFeeRequest,
  UpdateDieCuttingPlateFeeRequest,
  DieCuttingPlateFeeListParams,
} from '@/types/craftSalary';
import { PaginatedData, Result } from '@/types/common';
import {
  DashboardStats,
  GetDashboardStatsParams
} from '@/types/dashboard';

/* =========================
 * 新版本API - 使用Result类型 
 * 推荐新代码使用这些API
 * ========================= */

// 盒型相关API - 版本（使用Result类型）
export const boxApi = {
  /**
   * 获取盒型列表 - Result类型版本
   */
  getList: (params: BoxListParams): Promise<Result<PaginatedData<Box>>> => {
    return resultApi.post<PaginatedData<Box>>('/api/v1/admin/box/getList', params);
  },

  /**
   * 获取盒型详情 - Result类型版本
   */
  getDetail: (id: number): Promise<Result<Box>> => {
    return resultApi.post<Box>(`/api/v1/admin/box/getDetail`,  { id });
  },

  /**
   * 创建盒型 - Result类型版本
   */
  create: (params: BoxCreateParams): Promise<Result<Box>> => {
    return resultApi.post<Box>('/api/v1/admin/box/create', params);
  },

  /**
   * 更新盒型 - Result类型版本
   */
  update: (params: BoxUpdateParams & { id: number }): Promise<Result<Box>> => {
    return resultApi.post<Box>('/api/v1/admin/box/update', params);
  },

  /**
   * 删除盒型 - Result类型版本
   */
  delete: (id: number): Promise<Result<{ id: number }>> => {
    return resultApi.post<{ id: number }>('/api/v1/admin/box/delete',  { id });
  },

  /**
   * 获取盒型图片URL
   */
  getImageUrl: (id: number) => {
    return `/api/v1/admin/box/getImage?id=${id}`;
  },
};

// 自定义公式 API - Result类型版本
export const customFormulaApi = {
  /**
   * 获取自定义公式列表 - Result类型版本
   */
  getList: (params: CustomFormulaListParams): Promise<Result<PaginatedData<CustomFormula>>> => {
    return resultApi.post<PaginatedData<CustomFormula>>('/api/v1/admin/customFormula/getList', params);
  },

  /**
   * 获取自定义公式详情 - Result类型版本
   */
  getDetail: (id: number): Promise<Result<CustomFormula>> => {
    return resultApi.post<CustomFormula>(`/api/v1/admin/customFormula/getDetail`, { id });
  },

  /**
   * 创建自定义公式 - Result类型版本
   */
  create: (data: CustomFormulaCreateParams): Promise<Result<CustomFormula>> => {
    return resultApi.post<CustomFormula>('/api/v1/admin/customFormula/create', data);
  },

  /**
   * 更新自定义公式 - Result类型版本
   */
  update: (data: CustomFormulaUpdateParams): Promise<Result<CustomFormula>> => {
    return resultApi.post<CustomFormula>('/api/v1/admin/customFormula/update', data);
  },

  /**
   * 删除自定义公式 - Result类型版本
   */
  delete: (id: number): Promise<Result<{ id: number }>> => {
    return resultApi.post<{ id: number }>('/api/v1/admin/customFormula/delete', { id });
  },
};


// 特种纸 API - 新错误处理机制  
export const specialPaperApi = {
  /**
   * 获取特种纸列表
   */
  getList: async (params: MaterialListParams): Promise<Result<PaginatedData<SpecialPaper>>> => {
    return resultApi.post<PaginatedData<SpecialPaper>>('/api/v1/admin/material/specialPaper/getList', params);
  },

  /**
   * 获取特种纸详情
   */
  getDetail: async (id: number): Promise<Result<SpecialPaper>> => {
    return resultApi.post<SpecialPaper>(`/api/v1/admin/material/specialPaper/getDetail`, { id });
  },

  /**
   * 创建特种纸
   */
  create: async (data: SpecialPaperCreateParams): Promise<Result<SpecialPaper>> => {
    return resultApi.post<SpecialPaper>('/api/v1/admin/material/specialPaper/create', data);
  },

  /**
   * 更新特种纸
   */
  update: async (data: SpecialPaperUpdateParams): Promise<Result<SpecialPaper>> => {
    return resultApi.post<SpecialPaper>('/api/v1/admin/material/specialPaper/update', data);
  },

  /**
   * 删除特种纸
   */ 
  delete: async (id: number): Promise<Result<null>> => {
    return resultApi.post<null>(`/api/v1/admin/material/specialPaper/delete`, { id });
  },

  /**
   * 获取特种纸品类列表
   */
  getCategoryList: async (): Promise<Result<string[]>> => {
    return resultApi.post<string[]>('/api/v1/admin/material/specialPaper/getCategoryList');
  },
};

// 灰板纸 API - 新错误处理机制
export const greyBoardApi = {
  /**
   * 获取灰板纸列表
   */
  getList: async (data: MaterialListParams): Promise<Result<PaginatedData<GreyBoard>>> => {
    return resultApi.post<PaginatedData<GreyBoard>>('/api/v1/admin/material/greyBoard/getList', data);
  },

  /**
   * 获取灰板纸详情
   */
  getDetail: async (id: number): Promise<Result<GreyBoard>> => {
    return resultApi.post<GreyBoard>(`/api/v1/admin/material/greyBoard/getDetail`, { id });
  },

  /**
   * 创建灰板纸
   */
  create: async (data: GreyBoardCreateParams): Promise<Result<GreyBoard>> => {
    return resultApi.post<GreyBoard>('/api/v1/admin/material/greyBoard/create', data);
  },

  /**
   * 更新灰板纸
   */
  update: async (data: GreyBoardUpdateParams): Promise<Result<GreyBoard>> => {
    return resultApi.post<GreyBoard>('/api/v1/admin/material/greyBoard/update', data);
  },

  /**
   * 删除灰板纸
   */
  delete: async (id: number): Promise<Result<null>> => {
    return resultApi.post<null>(`/api/v1/admin/material/greyBoard/delete`, { id });
  },

  /**
   * 获取灰板纸品类列表
   */
  getCategoryList: async (): Promise<Result<string[]>> => {
    return resultApi.post<string[]>('/api/v1/admin/material/greyBoard/getCategoryList');
  },
};

// 灰板纸分切尺寸 API - 新错误处理机制
export const greyBoardCuttingApi = {
  /**
   * 获取分切尺寸列表
   */
  getList: async (data: MaterialListParams): Promise<Result<PaginatedData<GreyBoardCutting>>> => {
    return resultApi.post<PaginatedData<GreyBoardCutting>>('/api/v1/admin/material/greyBoardCutting/getList', data);
  },

  /**
   * 获取分切尺寸详情
   */
  getDetail: async (id: number): Promise<Result<GreyBoardCutting>> => {
    return resultApi.post<GreyBoardCutting>(`/api/v1/admin/material/greyBoardCutting/getDetail`, { id });
  },

  /**
   * 创建分切尺寸
   */
  create: async (data: GreyBoardCuttingCreateParams): Promise<Result<GreyBoardCutting>> => {
    return resultApi.post<GreyBoardCutting>('/api/v1/admin/material/greyBoardCutting/create', data);
  },

  /**
   * 更新分切尺寸
   */
  update: async (data: GreyBoardCuttingUpdateParams): Promise<Result<GreyBoardCutting>> => {
    return resultApi.post<GreyBoardCutting>('/api/v1/admin/material/greyBoardCutting/update', data);
  },

  /**
   * 删除分切尺寸
   */
  delete: async (id: number): Promise<Result<null>> => {
    return resultApi.post<null>(`/api/v1/admin/material/greyBoardCutting/delete`, { id });
  },
};

// 纸张材料 API - 新错误处理机制
export const paperApi = {
  /**
   * 获取纸张列表
   */
  getList: async (data: MaterialListParams): Promise<Result<PaginatedData<Paper>>> => {
    return resultApi.post<PaginatedData<Paper>>('/api/v1/admin/material/paper/getList', data);
  },

  /**
   * 获取纸张详情
   */
  getDetail: async (id: number): Promise<Result<Paper>> => {
    return resultApi.post<Paper>(`/api/v1/admin/material/paper/getDetail`, { id });
  },

  /**
   * 创建纸张
   */
  create: async (data: PaperCreateParams): Promise<Result<Paper>> => {
    return resultApi.post<Paper>('/api/v1/admin/material/paper/create', data);
  },

  /**
   * 更新纸张
   */
  update: async (data: PaperUpdateParams): Promise<Result<Paper>> => {
    return resultApi.post<Paper>('/api/v1/admin/material/paper/update', data);
  },

  /**
   * 删除纸张
   */
  delete: async (id: number): Promise<Result<null>> => {
    return resultApi.post<null>(`/api/v1/admin/material/paper/delete`, { id });
  },

  /**
   * 获取纸张品类列表
   */
  getCategoryList: async (): Promise<Result<string[]>> => {
    return resultApi.post<string[]>('/api/v1/admin/material/paper/getCategoryList');
  },
};

// 纸类分切尺寸 API - 新错误处理机制
export const paperCuttingApi = {
  /**
   * 获取分切尺寸列表
   */
  getList: async (data: MaterialListParams): Promise<Result<PaginatedData<PaperCutting>>> => {
    return resultApi.post<PaginatedData<PaperCutting>>('/api/v1/admin/material/paperCutting/getList', data);
  },

  /**
   * 获取分切尺寸详情
   */
  getDetail: async (id: number): Promise<Result<PaperCutting>> => {
    return resultApi.post<PaperCutting>(`/api/v1/admin/material/paperCutting/getDetail`, { id });
  },

  /**
   * 创建分切尺寸
   */
  create: async (data: PaperCuttingCreateParams): Promise<Result<PaperCutting>> => {
    return resultApi.post<PaperCutting>('/api/v1/admin/material/paperCutting/create', data);
  },

  /**
   * 更新分切尺寸
   */
  update: async (data: PaperCuttingUpdateParams): Promise<Result<PaperCutting>> => {
    return resultApi.post<PaperCutting>('/api/v1/admin/material/paperCutting/update', data);
  },

  /**
   * 删除分切尺寸
   */
  delete: async (id: number): Promise<Result<null>> => {
    return resultApi.post<null>(`/api/v1/admin/material/paperCutting/delete`, { id });
  },
};

// 不干胶材料API  (使用新的错误处理机制)
export const stickerApi = {
  /**
   * 获取不干胶材料列表
   */
  getList: (data: MaterialListParams) =>
    resultApi.post<PaginatedData<Sticker>>('/api/v1/admin/material/sticker/getList', data),

  /**
   * 获取不干胶材料详情
   */
  getDetail: (id: number) =>
    resultApi.post<Sticker>(`/api/v1/admin/material/sticker/getDetail`, { id }),

  /**
   * 创建不干胶材料
   */
  create: (data: StickerCreateParams) =>
    resultApi.post<Sticker>('/api/v1/admin/material/sticker/create', data),

  /**
   * 更新不干胶材料
   */
  update: (data: StickerUpdateParams) =>
    resultApi.post<Sticker>('/api/v1/admin/material/sticker/update', data),

  /**
   * 删除不干胶材料
   */
  delete: (id: number) =>
    resultApi.post<Sticker>(`/api/v1/admin/material/sticker/delete`, { id }),

  /**
   * 获取不干胶材料品类列表
   */
  getCategoryList: () =>
    resultApi.post<string[]>('/api/v1/admin/material/sticker/getCategoryList'),
};

// 特殊材料API  (使用新的错误处理机制)
export const specialMaterialApi = {
  /**
   * 获取特殊材料列表
   */
  getList: (data: MaterialListParams) =>
    resultApi.post<PaginatedData<SpecialMaterial>>('/api/v1/admin/material/specialMaterial/getList', data),

  /**
   * 获取特殊材料详情
   */
  getDetail: (id: number) =>
    resultApi.post<SpecialMaterial>(`/api/v1/admin/material/specialMaterial/getDetail`, { id }),

  /**
   * 创建特殊材料
   */
  create: (data: SpecialMaterialCreateParams) =>
    resultApi.post<SpecialMaterial>('/api/v1/admin/material/specialMaterial/create', data),

  /**
   * 更新特殊材料
   */
  update: (data: SpecialMaterialUpdateParams) =>
    resultApi.post<SpecialMaterial>('/api/v1/admin/material/specialMaterial/update', data),

  /**
   * 删除特殊材料
   */
  delete: (id: number) =>
    resultApi.post<SpecialMaterial>(`/api/v1/admin/material/specialMaterial/delete`, { id }),

  /**
   * 获取特殊材料品类列表
   */
  getCategoryList: () =>
    resultApi.post<string[]>('/api/v1/admin/material/specialMaterial/getCategoryList'),
};

// 配件API  (使用新的错误处理机制)
export const accessoryApi = {
  /**
   * 获取配件列表
   */
  getList: (data: MaterialListParams) =>
    resultApi.post<PaginatedData<Accessory>>('/api/v1/admin/material/accessory/getList', data),

  /**
   * 创建配件
   */
  create: (data: AccessoryCreateParams) =>
    resultApi.post<Accessory>('/api/v1/admin/material/accessory/create', data),

  /**
   * 更新配件
   */
  update: (data: AccessoryUpdateParams) =>
    resultApi.post<Accessory>('/api/v1/admin/material/accessory/update', data),

  /**
   * 删除配件
   */
  delete: (id: number) =>
    resultApi.post<boolean>('/api/v1/admin/material/accessory/delete', { id }),
};

// 礼盒配件API  (使用新的错误处理机制)
export const giftBoxaccessoryApi = {
  /**
   * 获取礼盒配件列表
   */
  getList: (data: MaterialListParams) =>
    resultApi.post<PaginatedData<GiftBoxAccessory>>('/api/v1/admin/material/giftBoxAccessory/getList', data),

  /**
   * 创建礼盒配件
   */
  create: (data: GiftBoxAccessoryCreateParams) =>
    resultApi.post<GiftBoxAccessory>('/api/v1/admin/material/giftBoxAccessory/create', data),

  /**
   * 更新礼盒配件
   */
  update: (data: GiftBoxAccessoryUpdateParams) =>
    resultApi.post<GiftBoxAccessory>('/api/v1/admin/material/giftBoxAccessory/update', data),

  /**
   * 删除礼盒配件
   */
  delete: (id: number) =>
    resultApi.post<boolean>('/api/v1/admin/material/giftBoxAccessory/delete', { id }),
};

// 纸箱材料API  (使用新的错误处理机制)
export const boxMaterialApi = {
  /**
   * 获取纸箱材料列表
   */
  getList: (data: BoxMaterialListParams) =>
    resultApi.post<PaginatedData<BoxMaterial>>('/api/v1/admin/material/boxMaterial/getList', data),

  /**
   * 获取纸箱材料详情
   */
  getDetail: (id: number) =>
    resultApi.post<BoxMaterial>(`/api/v1/admin/material/boxMaterial/getDetail`, { id }),

  /**
   * 创建纸箱材料
   */
  create: (data: BoxMaterialCreateParams) =>
    resultApi.post<BoxMaterial>('/api/v1/admin/material/boxMaterial/create', data),

  /**
   * 更新纸箱材料
   */
  update: (data: BoxMaterialUpdateParams) =>
    resultApi.post<BoxMaterial>('/api/v1/admin/material/boxMaterial/update', data),

  /**
   * 删除纸箱材料
   */
  delete: (id: number) =>
    resultApi.post<boolean>('/api/v1/admin/material/boxMaterial/delete', { id }),
};

// 工艺工资-印刷 API (使用新的错误处理机制)
export const printingApi = {
  /**
   * 获取印刷数据列表
   */
  getList: (data: PrintingListParams) =>
    resultApi.post<PaginatedData<Printing>>('/api/v1/admin/craftSalary/printing/getList', data),

  /**
   * 获取印刷数据详情
   */
  getDetail: (id: number) =>
    resultApi.post<Printing>(`/api/v1/admin/craftSalary/printing/getDetail`, { id }),

  /**
   * 创建印刷数据
   */
  create: (data: CreatePrintingRequest) =>
    resultApi.post<Printing>('/api/v1/admin/craftSalary/printing/create', data),

  /**
   * 更新印刷数据
   */
  update: (data: UpdatePrintingRequest) =>
    resultApi.post<Printing>('/api/v1/admin/craftSalary/printing/update', data),

  /**
   * 删除印刷数据
   */
  delete: (id: number) =>
    resultApi.post<{ id: number }>(`/api/v1/admin/craftSalary/printing/delete`, { id }),
};

// 工艺工资-印刷机 API (使用新的错误处理机制)  
export const printingMachineApi = {
  /**
   * 获取印刷机数据列表
   */
  getList: (data: PrintingMachineListParams) =>
    resultApi.post<PaginatedData<PrintingMachine>>('/api/v1/admin/craftSalary/printingMachine/getList', data),

  /**
   * 获取印刷机数据详情
   */
  getDetail: (id: number) =>
    resultApi.post<PrintingMachine>(`/api/v1/admin/craftSalary/printingMachine/getDetail`, { id }),

  /**
   * 创建印刷机数据
   */
  create: (data: CreatePrintingMachineRequest) =>
    resultApi.post<PrintingMachine>('/api/v1/admin/craftSalary/printingMachine/create', data),

  /**
   * 更新印刷机数据
   */
  update: (data: UpdatePrintingMachineRequest) =>
    resultApi.post<PrintingMachine>('/api/v1/admin/craftSalary/printingMachine/update', data),

  /**
   * 删除印刷机数据
   */
  delete: (id: number) =>
    resultApi.post<{ id: number }>(`/api/v1/admin/craftSalary/printingMachine/delete`, { id }),

  /**
   * 获取所有印刷机简要信息（用于下拉选择）
   */
  getOptions: async () => {
    const result = await printingMachineApi.getList({ pageSize: 100 });
    if (result.success && result.data) {
      return {
        success: true,
        data: result.data.list.map(machine => ({
          value: machine.id,
          label: machine.machineName,
          maxLength: machine.maxLength,
          maxWidth: machine.maxWidth
        }))
      };
    }
    return { success: false, data: [] };
  },
};

// 工艺工资-覆膜工艺 API (使用新的错误处理机制)
export const surfaceProcessApi = {
  /**
   * 获取覆膜工艺数据列表
   */
  getList: (data: SurfaceProcessListParams) =>
    resultApi.post<PaginatedData<SurfaceProcess>>('/api/v1/admin/craftSalary/surfaceProcess/getList', data),

  /**
   * 获取覆膜工艺数据详情
   */
  getDetail: (id: number) =>
    resultApi.post<SurfaceProcess>(`/api/v1/admin/craftSalary/surfaceProcess/getDetail`, { id }),

  /**
   * 创建覆膜工艺数据
   */
  create: (data: CreateSurfaceProcessRequest) =>
    resultApi.post<SurfaceProcess>('/api/v1/admin/craftSalary/surfaceProcess/create', data),

  /**
   * 更新覆膜工艺数据
   */
  update: (data: UpdateSurfaceProcessRequest) =>
    resultApi.post<SurfaceProcess>('/api/v1/admin/craftSalary/surfaceProcess/update', data),

  /**
   * 删除覆膜工艺数据
   */
  delete: (id: number) =>
    resultApi.post<{ id: number }>(`/api/v1/admin/craftSalary/surfaceProcess/delete`, { id }),
};

// 工艺工资-丝印工艺 API (使用新的错误处理机制)
export const silkScreenProcessApi = {
  /**
   * 获取丝印工艺数据列表
   */
  getList: (data: SilkScreenProcessListParams) =>
    resultApi.post<PaginatedData<SilkScreenProcess>>('/api/v1/admin/craftSalary/silkScreenProcess/getList', data),

  /**
   * 获取丝印工艺数据详情
   */
  getDetail: (id: number) =>
    resultApi.post<SilkScreenProcess>(`/api/v1/admin/craftSalary/silkScreenProcess/getDetail`, { id }),

  /**
   * 创建丝印工艺数据
   */
  create: (data: CreateSilkScreenProcessRequest) =>
    resultApi.post<SilkScreenProcess>('/api/v1/admin/craftSalary/silkScreenProcess/create', data),

  /**
   * 更新丝印工艺数据
   */
  update: (data: UpdateSilkScreenProcessRequest) =>
    resultApi.post<SilkScreenProcess>('/api/v1/admin/craftSalary/silkScreenProcess/update', data),

  /**
   * 删除丝印工艺数据
   */
  delete: (id: number) =>
    resultApi.post<{ id: number }>(`/api/v1/admin/craftSalary/silkScreenProcess/delete`, { id }),

  /**
   * 批量删除丝印工艺数据
   */
  batchDelete: async (ids: number[]) => {
    const results = await Promise.allSettled(
      ids.map(id => silkScreenProcessApi.delete(id))
    );
    return results;
  },
};

// 工艺工资-瓦楞工艺 API (使用新的错误处理机制)
export const corrugatedProcessApi = {
  /**
   * 获取瓦楞工艺数据列表
   */
  getList: (data: CorrugatedProcessListParams) =>
    resultApi.post<PaginatedData<CorrugatedProcess>>('/api/v1/admin/craftSalary/corrugatedProcess/getList', data),

  /**
   * 获取瓦楞工艺数据详情
   */
  getDetail: (id: number) =>
    resultApi.post<CorrugatedProcess>(`/api/v1/admin/craftSalary/corrugatedProcess/getDetail`, { id }),

  /**
   * 创建瓦楞工艺数据
   */
  create: (data: CreateCorrugatedProcessRequest) =>
    resultApi.post<CorrugatedProcess>('/api/v1/admin/craftSalary/corrugatedProcess/create', data),

  /**
   * 更新瓦楞工艺数据
   */
  update: (data: UpdateCorrugatedProcessRequest) =>
    resultApi.post<CorrugatedProcess>('/api/v1/admin/craftSalary/corrugatedProcess/update', data),

  /**
   * 删除瓦楞工艺数据
   */
  delete: (id: number) =>
    resultApi.post<{ id: number }>(`/api/v1/admin/craftSalary/corrugatedProcess/delete`, { id }),

  /**
   * 批量删除瓦楞工艺数据
   */
  batchDelete: async (ids: number[]) => {
    const results = await Promise.allSettled(
      ids.map(id => corrugatedProcessApi.delete(id))
    );
    return results;
  },
};

// 工艺工资-瓦楞率配置 API (使用新的错误处理机制)
export const corrugatedRateApi = {
  /**
   * 获取瓦楞率配置列表
   */
  getList: (data: CorrugatedRateListParams) =>
    resultApi.post<PaginatedData<CorrugatedRate>>('/api/v1/admin/craftSalary/corrugatedRate/getList', data),

  /**
   * 创建瓦楞率配置
   */
  create: (data: CreateCorrugatedRateRequest) =>
    resultApi.post<CorrugatedRate>('/api/v1/admin/craftSalary/corrugatedRate/create', data),

  /**
   * 更新瓦楞率配置
   */
  update: (data: UpdateCorrugatedRateRequest) =>
    resultApi.post<CorrugatedRate>('/api/v1/admin/craftSalary/corrugatedRate/update', data),

  /**
   * 删除瓦楞率配置
   */
  delete: (id: number) =>
    resultApi.post<{ id: number }>(`/api/v1/admin/craftSalary/corrugatedRate/delete`, { id }),

  /**
   * 获取所有瓦楞率配置（用于下拉选择）
   */
  getOptions: async () => {
    const result = await corrugatedRateApi.getList({ pageSize: 10 });
    if (result.success && result.data) {
      return {
        success: true,
        data: result.data.list.map(rate => ({
          value: rate.fluteType,
          label: `${rate.fluteType} (${rate.rate})`,
          rate: rate.rate
        }))
      };
    }
    return { success: false, data: [] };
  },
};

// 工艺工资-对裱工艺 API (使用新的错误处理机制)
export const laminatingProcessApi = {
  /**
   * 获取对裱工艺数据列表
   */
  getList: (data: LaminatingProcessListParams) =>
    resultApi.post<PaginatedData<LaminatingProcess>>('/api/v1/admin/craftSalary/laminatingProcess/getList', data),

  /**
   * 获取对裱工艺数据详情
   */
  getDetail: (id: number) =>
    resultApi.post<LaminatingProcess>(`/api/v1/admin/craftSalary/laminatingProcess/getDetail`, { id }),

  /**
   * 创建对裱工艺数据
   */
  create: (data: CreateLaminatingProcessRequest) =>
    resultApi.post<LaminatingProcess>('/api/v1/admin/craftSalary/laminatingProcess/create', data),

  /**
   * 更新对裱工艺数据
   */
  update: (data: UpdateLaminatingProcessRequest) =>
    resultApi.post<LaminatingProcess>('/api/v1/admin/craftSalary/laminatingProcess/update', data),

  /**
   * 删除对裱工艺数据
   */
  delete: (id: number) =>
    resultApi.post<{ id: number }>(`/api/v1/admin/craftSalary/laminatingProcess/delete`, { id }),

  /**
   * 批量删除对裱工艺数据
   */
  batchDelete: async (ids: number[]) => {
    const results = await Promise.allSettled(
      ids.map(id => laminatingProcessApi.delete(id))
    );
    return results;
  },
};

// 工艺工资-烫金工艺 API (使用新的错误处理机制)
export const hotStampingProcessApi = {
  /**
   * 获取烫金工艺数据列表
   */
  getList: (data: HotStampingProcessListParams) =>
    resultApi.post<PaginatedData<HotStampingProcess>>('/api/v1/admin/craftSalary/hotStampingProcess/getList', data),

  /**
   * 获取烫金工艺数据详情
   */
  getDetail: (id: number) =>
    resultApi.post<HotStampingProcess>(`/api/v1/admin/craftSalary/hotStampingProcess/getDetail`, { id }),

  /**
   * 创建烫金工艺数据
   */
  create: (data: CreateHotStampingProcessRequest) =>
    resultApi.post<HotStampingProcess>('/api/v1/admin/craftSalary/hotStampingProcess/create', data),

  /**
   * 更新烫金工艺数据
   */
  update: (data: UpdateHotStampingProcessRequest) =>
    resultApi.post<HotStampingProcess>('/api/v1/admin/craftSalary/hotStampingProcess/update', data),

  /**
   * 删除烫金工艺数据
   */
  delete: (id: number) =>
    resultApi.post<{ id: number }>(`/api/v1/admin/craftSalary/hotStampingProcess/delete`, { id }),

  /**
   * 批量删除烫金工艺数据
   */
  batchDelete: (ids: number[]) =>
    Promise.all(ids.map(id => resultApi.post<{ id: number }>(`/api/v1/admin/craftSalary/hotStampingProcess/delete`, { id }))),
};

// 工艺工资-烫金版费 API (使用新的错误处理机制)
export const hotStampingPlateFeeApi = {
  /**
   * 获取烫金版费数据列表
   */
  getList: (data: HotStampingPlateFeeListParams) =>
    resultApi.post<PaginatedData<HotStampingPlateFee>>('/api/v1/admin/craftSalary/hotStampingPlateFee/getList', data),

  /**
   * 创建烫金版费数据
   */
  create: (data: CreateHotStampingPlateFeeRequest) =>
    resultApi.post<HotStampingPlateFee>('/api/v1/admin/craftSalary/hotStampingPlateFee/create', data),

  /**
   * 更新烫金版费数据
   */
  update: (data: UpdateHotStampingPlateFeeRequest) =>
    resultApi.post<HotStampingPlateFee>('/api/v1/admin/craftSalary/hotStampingPlateFee/update', data),

  /**
   * 删除烫金版费数据
   */
  delete: (id: number) =>
    resultApi.post<{ id: number }>(`/api/v1/admin/craftSalary/hotStampingPlateFee/delete`, { id }),

};

// 工艺工资-压纹工艺 API (使用新的错误处理机制)
export const texturingProcessApi = {
  /**
   * 获取压纹工艺数据列表
   */
  getList: (data: TexturingProcessListParams) =>
    resultApi.post<PaginatedData<TexturingProcess>>('/api/v1/admin/craftSalary/texturingProcess/getList', data),

  /**
   * 获取压纹工艺数据详情
   */
  getDetail: (id: number) =>
    resultApi.post<TexturingProcess>(`/api/v1/admin/craftSalary/texturingProcess/getDetail`, { id }),

  /**
   * 创建压纹工艺数据
   */
  create: (data: CreateTexturingProcessRequest) =>
    resultApi.post<TexturingProcess>('/api/v1/admin/craftSalary/texturingProcess/create', data),

  /**
   * 更新压纹工艺数据
   */
  update: (data: UpdateTexturingProcessRequest) =>
    resultApi.post<TexturingProcess>('/api/v1/admin/craftSalary/texturingProcess/update', data),

  /**
   * 删除压纹工艺数据
   */
  delete: (id: number) =>
    resultApi.post<{ id: number }>(`/api/v1/admin/craftSalary/texturingProcess/delete`, { id }),

  /**
   * 批量删除压纹工艺数据
   */
  batchDelete: async (ids: number[]) => {
    const results = await Promise.allSettled(
      ids.map(id => texturingProcessApi.delete(id))
    );
    return results;
  },
};

// 工艺工资-凹凸工艺 API (使用新的错误处理机制)
export const embossingProcessApi = {
  /**
   * 获取凹凸工艺数据列表
   */
  getList: (data: EmbossingProcessListParams) =>
    resultApi.post<PaginatedData<EmbossingProcess>>('/api/v1/admin/craftSalary/embossingProcess/getList', data),

  /**
   * 获取凹凸工艺数据详情
   */
  getDetail: (id: number) =>
    resultApi.post<EmbossingProcess>(`/api/v1/admin/craftSalary/embossingProcess/getDetail`, { id }),

  /**
   * 创建凹凸工艺数据
   */
  create: (data: CreateEmbossingProcessRequest) =>
    resultApi.post<EmbossingProcess>('/api/v1/admin/craftSalary/embossingProcess/create', data),

  /**
   * 更新凹凸工艺数据
   */
  update: (data: UpdateEmbossingProcessRequest) =>
    resultApi.post<EmbossingProcess>('/api/v1/admin/craftSalary/embossingProcess/update', data),

  /**
   * 删除凹凸工艺数据
   */
  delete: (id: number) =>
    resultApi.post<{ id: number }>(`/api/v1/admin/craftSalary/embossingProcess/delete`, { id }),

  /**
   * 批量删除凹凸工艺数据
   */
  batchDelete: async (ids: number[]) => {
    const results = await Promise.allSettled(
      ids.map(id => embossingProcessApi.delete(id))
    );
    return results;
  },
};

// 工艺工资-液压工艺 API (使用新的错误处理机制)
export const hydraulicProcessApi = {
  /**
   * 获取液压工艺数据列表
   */
  getList: (data: HydraulicProcessListParams) =>
    resultApi.post<PaginatedData<HydraulicProcess>>('/api/v1/admin/craftSalary/hydraulicProcess/getList', data),

  /**
   * 获取液压工艺数据详情
   */
  getDetail: (id: number) =>
    resultApi.post<HydraulicProcess>(`/api/v1/admin/craftSalary/hydraulicProcess/getDetail`, { id }),

  /**
   * 创建液压工艺数据
   */
  create: (data: CreateHydraulicProcessRequest) =>
    resultApi.post<HydraulicProcess>('/api/v1/admin/craftSalary/hydraulicProcess/create', data),

  /**
   * 更新液压工艺数据
   */
  update: (data: UpdateHydraulicProcessRequest) =>
    resultApi.post<HydraulicProcess>('/api/v1/admin/craftSalary/hydraulicProcess/update', data),

  /**
   * 删除液压工艺数据
   */
  delete: (id: number) =>
    resultApi.post<{ id: number }>(`/api/v1/admin/craftSalary/hydraulicProcess/delete`, { id }),

  /**
   * 批量删除液压工艺数据
   */
  batchDelete: async (ids: number[]) => {
    const results = await Promise.allSettled(
      ids.map(id => hydraulicProcessApi.delete(id))
    );
    return results;
  },
};

// 工艺工资-加工费配置 API (使用新的错误处理机制)
export const processingFeeApi = {
  /**
   * 获取加工费数据列表
   */
  getList: (params: ProcessingFeeListParams) =>
    resultApi.post<PaginatedData<ProcessingFee>>('/api/v1/admin/craftSalary/processingFee/getList', params),

  /**
   * 创建加工费数据
   */
  create: (data: CreateProcessingFeeRequest) =>
    resultApi.post<ProcessingFee>('/api/v1/admin/craftSalary/processingFee/create', data),

  /**
   * 更新加工费数据
   */
  update: (data: UpdateProcessingFeeRequest) =>
    resultApi.post<ProcessingFee>('/api/v1/admin/craftSalary/processingFee/update', data),

  /**
   * 删除加工费数据
   */
  delete: (id: number) =>
    resultApi.post<{ id: number }>('/api/v1/admin/craftSalary/processingFee/delete', { id }),

  /**
   * 批量删除加工费数据
   */
  batchDelete: async (ids: number[]) => {
    const results = await Promise.allSettled(
      ids.map(id => processingFeeApi.delete(id))
    );
    return results;
  },
};

// 工艺工资-加工费固定参数 API (使用新的错误处理机制)
export const processingParamsApi = {
  /**
   * 获取固定参数配置
   */
  get: () =>
    resultApi.post<ProcessingParams>('/api/v1/admin/craftSalary/processingParams/get'),

  /**
   * 更新固定参数配置
   */
  update: (data: UpdateProcessingParamsRequest) =>
    resultApi.post<ProcessingParams>('/api/v1/admin/craftSalary/processingParams/update', data),
};

// 材料尺寸配置 API (使用新的错误处理机制)
export const materialSizeApi = {
  /**
   * 获取材料尺寸配置列表
   */
  getList: () =>
    resultApi.post<{ list: any[], config: any }>('/api/v1/admin/material/materialSize/getList'),

  /**
   * 更新材料尺寸配置
   */
  update: (data: any) =>
    resultApi.post<any>('/api/v1/admin/material/materialSize/update', data),

  /**
   * 重置材料尺寸为默认值
   */
  reset: () =>
    resultApi.post<any>('/api/v1/admin/material/materialSize/reset'),
};

// 工艺工资-模切工艺 API (使用新的错误处理机制)
export const dieCuttingProcessApi = {
  /**
   * 获取模切工艺数据列表
   */
  getList: (data: DieCuttingProcessListParams) =>
    resultApi.post<PaginatedData<DieCuttingProcess>>('/api/v1/admin/craftSalary/dieCuttingProcess/getList', data),

  /**
   * 获取模切工艺数据详情
   */
  getDetail: (id: number) =>
    resultApi.post<DieCuttingProcess>(`/api/v1/admin/craftSalary/dieCuttingProcess/getDetail`, { id }),

  /**
   * 创建模切工艺数据
   */
  create: (data: CreateDieCuttingProcessRequest) =>
    resultApi.post<DieCuttingProcess>('/api/v1/admin/craftSalary/dieCuttingProcess/create', data),

  /**
   * 更新模切工艺数据
   */
  update: (data: UpdateDieCuttingProcessRequest) =>
    resultApi.post<DieCuttingProcess>('/api/v1/admin/craftSalary/dieCuttingProcess/update', data),

  /**
   * 删除模切工艺数据
   */
  delete: (id: number) =>
    resultApi.post<{ id: number }>(`/api/v1/admin/craftSalary/dieCuttingProcess/delete`, { id }),

  /**
   * 批量删除模切工艺数据
   */
  batchDelete: async (ids: number[]) => {
    const results = await Promise.allSettled(
      ids.map(id => dieCuttingProcessApi.delete(id))
    );
    return results;
  },
};

// 工艺工资-刀版费 API (使用新的错误处理机制)
export const dieCuttingPlateFeeApi = {
  /**
   * 获取刀版费数据列表
   */
  getList: (data: DieCuttingPlateFeeListParams) =>
    resultApi.post<PaginatedData<DieCuttingPlateFee>>('/api/v1/admin/craftSalary/dieCuttingPlateFee/getList', data),

  /**
   * 获取刀版费数据详情
   */
  getDetail: (id: number) =>
    resultApi.post<DieCuttingPlateFee>(`/api/v1/admin/craftSalary/dieCuttingPlateFee/getDetail`, { id }),

  /**
   * 创建刀版费数据
   */
  create: (data: CreateDieCuttingPlateFeeRequest) =>
    resultApi.post<DieCuttingPlateFee>('/api/v1/admin/craftSalary/dieCuttingPlateFee/create', data),

  /**
   * 更新刀版费数据
   */
  update: (data: UpdateDieCuttingPlateFeeRequest) =>
    resultApi.post<DieCuttingPlateFee>('/api/v1/admin/craftSalary/dieCuttingPlateFee/update', data),

  /**
   * 删除刀版费数据
   */
  delete: (id: number) =>
    resultApi.post<{ id: number }>(`/api/v1/admin/craftSalary/dieCuttingPlateFee/delete`, { id }),

  /**
   * 批量删除刀版费数据
   */
  batchDelete: async (ids: number[]) => {
    const results = await Promise.allSettled(
      ids.map(id => dieCuttingPlateFeeApi.delete(id))
    );
    return results;
  },
};

// 仪表盘相关API (使用新的错误处理机制)
export const dashboardApi = {
  /**
   * 获取仪表盘统计数据
   */
  getStats: (params: GetDashboardStatsParams = {}): Promise<Result<DashboardStats>> => {
    return resultApi.post<DashboardStats>('/api/v1/admin/dashboard/getStats', params);
  },
};
