# API接口文档

## 概述

本文档描述了印刷报价系统的后端API接口规范。所有API接口遵循RESTful设计原则，使用统一的响应格式和错误处理机制。

## 技术栈

- **框架**: Next.js 13.5.11 App Router
- **语言**: TypeScript 5
- **数据库**: MySQL
- **ORM**: Prisma 6.8.2
- **参数校验**: Zod 3.24.4
- **公式计算**: Math.js 14.5.0

## 基础信息

### 基础URL
```
开发环境: http://localhost:3000
生产环境: https://your-domain.com
```

### 接口路径规范
```
/api/v1/{模块}/{功能}
```

示例：
- `/api/v1/admin/box/getList` - 获取盒型列表（管理后台）
- `/api/v1/box/getList` - 获取盒型列表（公开接口）
- `/api/v1/admin/material/create` - 创建材料
- `/api/v1/admin/customFormula/update` - 更新自定义公式

## 统一响应格式

### Result<T> 类型定义

所有API接口统一使用 `Result<T>` 类型作为响应格式：

```typescript
// 成功响应
interface SuccessResult<T> {
  success: true;
  data: T;
  message?: string;
}

// 失败响应
interface ErrorResult {
  success: false;
  error: string;
  code?: string;
  details?: any;
}

type Result<T> = SuccessResult<T> | ErrorResult;
```

### 响应示例

**成功响应**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "标准盒型",
    "length": 100,
    "width": 80,
    "height": 50
  },
  "message": "操作成功"
}
```

**失败响应**:
```json
{
  "success": false,
  "error": "参数验证失败",
  "code": "VALIDATION_ERROR",
  "details": {
    "field": "length",
    "message": "长度必须大于0"
  }
}
```

## 错误码系统

### 通用错误码

| 错误码 | 描述 | HTTP状态码 |
|--------|------|------------|
| `VALIDATION_ERROR` | 参数验证失败 | 400 |
| `UNAUTHORIZED` | 未授权访问 | 401 |
| `FORBIDDEN` | 权限不足 | 403 |
| `NOT_FOUND` | 资源不存在 | 404 |
| `CONFLICT` | 资源冲突 | 409 |
| `INTERNAL_ERROR` | 服务器内部错误 | 500 |
| `DATABASE_ERROR` | 数据库操作失败 | 500 |

### 业务错误码

| 错误码 | 描述 |
|--------|------|
| `BOX_NOT_FOUND` | 盒型不存在 |
| `BOX_NAME_EXISTS` | 盒型名称已存在 |
| `MATERIAL_NOT_FOUND` | 材料不存在 |
| `FORMULA_INVALID` | 公式格式无效 |
| `FORMULA_CALCULATION_ERROR` | 公式计算错误 |

## 参数校验规范

### Zod校验模式

所有接口参数使用Zod进行严格校验，校验规则定义在 `src/lib/validations/` 目录下。

**校验失败响应格式**:
```json
{
  "success": false,
  "error": "参数验证失败",
  "code": "VALIDATION_ERROR",
  "details": {
    "field": "字段名",
    "message": "具体错误信息",
    "received": "实际接收值"
  }
}
```

### 通用校验规则

```typescript
// 分页参数
const paginationSchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(10)
});

// ID参数
const idSchema = z.object({
  id: z.number().positive("ID必须为正整数")
});

// 搜索参数
const searchSchema = z.object({
  keyword: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
});
```

## 管理后台接口

### 盒型管理

#### 获取盒型列表
```
POST /api/v1/admin/box/getList
```

**请求参数**:
```typescript
{
  page?: number;        // 页码，默认1
  pageSize?: number;    // 每页数量，默认10，最大100
  keyword?: string;     // 搜索关键词
  sortBy?: string;      // 排序字段
  sortOrder?: 'asc' | 'desc'; // 排序方向，默认desc
}
```

**响应数据**:
```typescript
{
  success: true,
  data: {
    list: Array<{
      id: number;
      name: string;
      length: number;
      width: number;
      height: number;
      description?: string;
      createdAt: string;
      updatedAt: string;
    }>,
    total: number;
    page: number;
    pageSize: number;
  }
}
```

#### 创建盒型
```
POST /api/v1/admin/box/create
```

**请求参数**:
```typescript
{
  name: string;         // 盒型名称，必填，1-50字符
  length: number;       // 长度，必填，大于0
  width: number;        // 宽度，必填，大于0
  height: number;       // 高度，必填，大于0
  description?: string; // 描述，可选，最大200字符
}
```

**校验规则**:
```typescript
const createBoxSchema = z.object({
  name: z.string()
    .min(1, "盒型名称不能为空")
    .max(50, "盒型名称不能超过50个字符"),
  length: z.number()
    .positive("长度必须大于0"),
  width: z.number()
    .positive("宽度必须大于0"),
  height: z.number()
    .positive("高度必须大于0"),
  description: z.string()
    .max(200, "描述不能超过200个字符")
    .optional()
});
```

**响应数据**:
```typescript
{
  success: true,
  data: {
    id: number;
    name: string;
    length: number;
    width: number;
    height: number;
    description?: string;
    createdAt: string;
    updatedAt: string;
  },
  message: "盒型创建成功"
}
```

#### 更新盒型
```
POST /api/v1/admin/box/update
```

**请求参数**:
```typescript
{
  id: number;           // 盒型ID，必填
  name?: string;        // 盒型名称，可选
  length?: number;      // 长度，可选
  width?: number;       // 宽度，可选
  height?: number;      // 高度，可选
  description?: string; // 描述，可选
}
```

#### 删除盒型
```
POST /api/v1/admin/box/delete
```

**请求参数**:
```typescript
{
  id: number; // 盒型ID，必填
}
```

### 材料管理

#### 获取材料列表
```
POST /api/v1/admin/material/getList
```

**请求参数**:
```typescript
{
  page?: number;
  pageSize?: number;
  keyword?: string;
  category?: string;    // 材料分类筛选
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
```

**响应数据**:
```typescript
{
  success: true,
  data: {
    list: Array<{
      id: number;
      name: string;
      category: string;
      unit: string;
      price: number;
      description?: string;
      createdAt: string;
      updatedAt: string;
    }>,
    total: number;
    page: number;
    pageSize: number;
  }
}
```

#### 创建材料
```
POST /api/v1/admin/material/create
```

**请求参数**:
```typescript
{
  name: string;         // 材料名称，必填
  category: string;     // 材料分类，必填
  unit: string;         // 计量单位，必填
  price: number;        // 单价，必填，大于等于0
  description?: string; // 描述，可选
}
```

**校验规则**:
```typescript
const createMaterialSchema = z.object({
  name: z.string()
    .min(1, "材料名称不能为空")
    .max(50, "材料名称不能超过50个字符"),
  category: z.string()
    .min(1, "材料分类不能为空"),
  unit: z.string()
    .min(1, "计量单位不能为空"),
  price: z.number()
    .min(0, "单价不能小于0"),
  description: z.string()
    .max(200, "描述不能超过200个字符")
    .optional()
});
```

### 自定义公式管理

#### 获取公式列表
```
POST /api/v1/admin/customFormula/getList
```

**响应数据**:
```typescript
{
  success: true,
  data: {
    list: Array<{
      id: number;
      name: string;
      formula: string;
      description?: string;
      variables: Array<{
        name: string;
        description: string;
        type: 'number' | 'string';
        required: boolean;
      }>;
      createdAt: string;
      updatedAt: string;
    }>,
    total: number;
    page: number;
    pageSize: number;
  }
}
```

#### 创建公式
```
POST /api/v1/admin/customFormula/create
```

**请求参数**:
```typescript
{
  name: string;         // 公式名称，必填
  formula: string;      // 公式表达式，必填
  description?: string; // 描述，可选
  variables: Array<{    // 变量定义，必填
    name: string;
    description: string;
    type: 'number' | 'string';
    required: boolean;
  }>;
}
```

**校验规则**:
```typescript
const createFormulaSchema = z.object({
  name: z.string()
    .min(1, "公式名称不能为空")
    .max(50, "公式名称不能超过50个字符"),
  formula: z.string()
    .min(1, "公式表达式不能为空"),
  description: z.string()
    .max(200, "描述不能超过200个字符")
    .optional(),
  variables: z.array(z.object({
    name: z.string().min(1, "变量名不能为空"),
    description: z.string().min(1, "变量描述不能为空"),
    type: z.enum(['number', 'string']),
    required: z.boolean()
  })).min(1, "至少需要定义一个变量")
});
```

#### 验证公式
```
POST /api/v1/admin/customFormula/validate
```

**请求参数**:
```typescript
{
  formula: string;      // 公式表达式
  variables: Record<string, any>; // 测试变量值
}
```

**响应数据**:
```typescript
{
  success: true,
  data: {
    isValid: boolean;
    result?: number;
    error?: string;
  }
}
```

## 工艺工资-加工费管理接口

### 加工费配置

#### 获取加工费列表
```
POST /api/v1/admin/craftSalary/processingFee/getList
```

**请求参数**:
```typescript
{
  page?: number;        // 页码，默认1
  pageSize?: number;    // 每页数量，默认10，最大100
  search?: string;      // 搜索关键词（名称、备注）
  unit?: ProcessingFeeUnit; // 单位筛选
  sortBy?: string;      // 排序字段，默认createdAt
  sortOrder?: 'asc' | 'desc'; // 排序方向，默认desc
}
```

**响应数据**:
```typescript
{
  success: true,
  data: {
    list: Array<{
      id: number;
      name: string;
      unitPrice: number;
      unit: ProcessingFeeUnit;
      basePrice: number;
      remark?: string;
      createdAt: string;
      updatedAt: string;
    }>,
    pagination: {
      page: number;
      pageSize: number;
      total: number;
    }
  },
  message: "获取加工费列表成功"
}
```

**校验规则**:
```typescript
const processingFeeListParamsSchema = z.object({
  page: z.number().int().positive().optional().default(1),
  pageSize: z.number().int().positive().max(100).optional().default(10),
  search: z.string().max(100).optional(),
  unit: z.enum(['元/个', '元/平方', '元/张']).optional(),
  sortBy: z.string().optional().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc')
});
```

#### 创建加工费
```
POST /api/v1/admin/craftSalary/processingFee/create
```

**请求参数**:
```typescript
{
  name: string;         // 加工费名称，必填
  unitPrice: number;    // 单价，必填，≥0
  unit: ProcessingFeeUnit; // 单位，必填
  basePrice: number;    // 起步价，必填，≥0
  remark?: string;      // 备注，可选
}
```

**响应数据**:
```typescript
{
  success: true,
  data: {
    id: number;
    name: string;
    unitPrice: number;
    unit: ProcessingFeeUnit;
    basePrice: number;
    remark?: string;
    createdAt: string;
    updatedAt: string;
  },
  message: "创建加工费成功"
}
```

**校验规则**:
```typescript
const createProcessingFeeSchema = z.object({
  name: z.string().min(1, '名称不能为空').max(100, '名称长度不能超过100个字符'),
  unitPrice: z.number().min(0, '单价不能为负数'),
  unit: z.enum(['元/个', '元/平方', '元/张']),
  basePrice: z.number().min(0, '起步价不能为负数'),
  remark: z.string().max(500, '备注长度不能超过500个字符').optional()
});
```

#### 更新加工费
```
POST /api/v1/admin/craftSalary/processingFee/update
```

**请求参数**:
```typescript
{
  id: number;           // 加工费ID，必填
  name: string;         // 加工费名称，必填
  unitPrice: number;    // 单价，必填，≥0
  unit: ProcessingFeeUnit; // 单位，必填
  basePrice: number;    // 起步价，必填，≥0
  remark?: string;      // 备注，可选
}
```

**响应数据**:
```typescript
{
  success: true,
  data: {
    id: number;
    name: string;
    unitPrice: number;
    unit: ProcessingFeeUnit;
    basePrice: number;
    remark?: string;
    createdAt: string;
    updatedAt: string;
  },
  message: "更新加工费成功"
}
```

#### 删除加工费
```
POST /api/v1/admin/craftSalary/processingFee/delete
```

**请求参数**:
```typescript
{
  id: number;           // 加工费ID，必填
}
```

**响应数据**:
```typescript
{
  success: true,
  data: null,
  message: "删除加工费成功"
}
```

### 固定参数配置

#### 获取固定参数配置
```
POST /api/v1/admin/craftSalary/processingParams/get
```

**请求参数**: 无

**响应数据**:
```typescript
{
  success: true,
  data: {
    id: number;
    pvcFilm: number;              // PVC贴膜价格（元/吨）
    slottingSalary: number;       // 开槽工资（元/个）
    slottingBasePrice: number;    // 开槽起步价（元）
    blisterPlate: number;         // 吸塑版价格（元/平方）
    blisterBasePrice: number;     // 吸塑起步价（元）
    highFrequencyPlate: number;   // 高频机版价格（元/平方）
    highFrequencyBasePrice: number; // 高频机起步价（元）
    sprayCodeFee: number;         // 喷码费用（元/个）
    sprayCodeBasePrice: number;   // 喷码起步价（元）
    inspectionFee: number;        // 检验费用（元/个）
    inspectionBasePrice: number;  // 检验起步价（元）
    createdAt: string;
    updatedAt: string;
  },
  message: "获取固定参数配置成功"
}
```

#### 更新固定参数配置
```
POST /api/v1/admin/craftSalary/processingParams/update
```

**请求参数**:
```typescript
{
  pvcFilm: number;              // PVC贴膜价格，必填，≥0
  slottingSalary: number;       // 开槽工资，必填，≥0
  slottingBasePrice: number;    // 开槽起步价，必填，≥0
  blisterPlate: number;         // 吸塑版价格，必填，≥0
  blisterBasePrice: number;     // 吸塑起步价，必填，≥0
  highFrequencyPlate: number;   // 高频机版价格，必填，≥0
  highFrequencyBasePrice: number; // 高频机起步价，必填，≥0
  sprayCodeFee: number;         // 喷码费用，必填，≥0
  sprayCodeBasePrice: number;   // 喷码起步价，必填，≥0
  inspectionFee: number;        // 检验费用，必填，≥0
  inspectionBasePrice: number;  // 检验起步价，必填，≥0
}
```

**响应数据**:
```typescript
{
  success: true,
  data: {
    id: number;
    pvcFilm: number;
    slottingSalary: number;
    slottingBasePrice: number;
    blisterPlate: number;
    blisterBasePrice: number;
    highFrequencyPlate: number;
    highFrequencyBasePrice: number;
    sprayCodeFee: number;
    sprayCodeBasePrice: number;
    inspectionFee: number;
    inspectionBasePrice: number;
    createdAt: string;
    updatedAt: string;
  },
  message: "更新固定参数配置成功"
}
```

**校验规则**:
```typescript
const updateProcessingParamsSchema = z.object({
  pvcFilm: z.number().min(0, 'PVC贴膜价格不能为负数'),
  slottingSalary: z.number().min(0, '开槽工资不能为负数'),
  slottingBasePrice: z.number().min(0, '开槽起步价不能为负数'),
  blisterPlate: z.number().min(0, '吸塑版价格不能为负数'),
  blisterBasePrice: z.number().min(0, '吸塑起步价不能为负数'),
  highFrequencyPlate: z.number().min(0, '高频机版价格不能为负数'),
  highFrequencyBasePrice: z.number().min(0, '高频机起步价不能为负数'),
  sprayCodeFee: z.number().min(0, '喷码费用不能为负数'),
  sprayCodeBasePrice: z.number().min(0, '喷码起步价不能为负数'),
  inspectionFee: z.number().min(0, '检验费用不能为负数'),
  inspectionBasePrice: z.number().min(0, '检验起步价不能为负数')
});
```

### 类型定义

```typescript
// 加工费单位枚举
type ProcessingFeeUnit = '元/个' | '元/平方' | '元/张';

// 加工费接口
interface ProcessingFee {
  id: number;
  name: string;
  unitPrice: number;
  unit: ProcessingFeeUnit;
  basePrice: number;
  remark?: string;
  isDel: boolean;
  createdAt: string;
  updatedAt: string;
}

// 固定参数接口
interface ProcessingParams {
  id: number;
  pvcFilm: number;
  slottingSalary: number;
  slottingBasePrice: number;
  blisterPlate: number;
  blisterBasePrice: number;
  highFrequencyPlate: number;
  highFrequencyBasePrice: number;
  sprayCodeFee: number;
  sprayCodeBasePrice: number;
  inspectionFee: number;
  inspectionBasePrice: number;
  createdAt: string;
  updatedAt: string;
}
```

### 错误码

| 错误码 | 描述 | 场景 |
|--------|------|------|
| `DUPLICATE_ENTRY` | 数据已存在 | 创建或更新时名称重复 |
| `RESOURCE_NOT_FOUND` | 资源不存在 | 更新或删除不存在的记录 |
| `VALIDATION_ERROR` | 参数验证失败 | 请求参数不符合校验规则 |

### 使用示例

```typescript
// 前端调用示例
import { processingFeeApi, processingParamsApi } from '@/services/adminApi';

// 获取加工费列表
const fetchFeeList = async () => {
  const result = await processingFeeApi.getList({
    page: 1,
    pageSize: 10,
    search: '印刷',
    unit: '元/平方'
  });
  
  if (result.success) {
    console.log('加工费列表:', result.data.list);
  }
};

// 创建加工费
const createFee = async () => {
  const result = await processingFeeApi.create({
    name: '印刷加工费',
    unitPrice: 0.5,
    unit: '元/平方',
    basePrice: 10,
    remark: '标准印刷加工费'
  });
  
  if (result.success) {
    console.log('创建成功:', result.data);
  }
};

// 更新固定参数
const updateParams = async () => {
  const result = await processingParamsApi.update({
    pvcFilm: 8000,
    slottingSalary: 0.1,
    slottingBasePrice: 50,
    // ... 其他参数
  });
  
  if (result.success) {
    console.log('更新成功:', result.data);
  }
};
```

---

**加工费模块接口文档 - 2024年12月更新**

## 错误处理

### withValidation 中间件

所有接口使用 `withValidation` 中间件进行参数校验：

```typescript
import { withValidation } from '@/lib/utils/withValidation';
import { createBoxSchema } from '@/lib/validations/admin/box';

export const POST = withValidation(createBoxSchema, async (req, validatedData) => {
  // 处理业务逻辑
  return apiResponse.success(result);
});
```

### 统一错误响应

```typescript
// 参数校验失败
return apiResponse.error("参数验证失败", "VALIDATION_ERROR", validationDetails);

// 业务逻辑错误
return apiResponse.error("盒型不存在", "BOX_NOT_FOUND");

// 数据库错误
return apiResponse.error("数据库操作失败", "DATABASE_ERROR");
```

## 开发规范

### 接口文件结构
```
src/app/api/v1/
├── admin/
│   ├── box/
│   │   ├── getList/
│   │   │   └── route.ts
│   │   ├── create/
│   │   │   └── route.ts
│   │   ├── update/
│   │   │   └── route.ts
│   │   └── delete/
│   │       └── route.ts
│   ├── material/
│   └── customFormula/
```

### 命名规范

1. **接口路径**: 使用小驼峰命名，如 `getList`, `createBox`
2. **参数字段**: 使用小驼峰命名，如 `pageSize`, `sortOrder`
3. **响应字段**: 使用小驼峰命名，如 `createdAt`, `updatedAt`
4. **错误码**: 使用大写下划线命名，如 `VALIDATION_ERROR`

### 类型定义

所有接口的请求和响应类型定义在 `src/types/` 目录下：

```typescript
// src/types/box.ts
export interface Box {
  id: number;
  name: string;
  length: number;
  width: number;
  height: number;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateBoxRequest {
  name: string;
  length: number;
  width: number;
  height: number;
  description?: string;
}

export interface BoxListResponse {
  list: Box[];
  total: number;
  page: number;
  pageSize: number;
}
```

### 最佳实践

1. **参数校验**: 所有接口必须使用Zod进行参数校验
2. **错误处理**: 使用统一的错误响应格式和错误码
3. **类型安全**: 严格定义TypeScript类型，避免使用any
4. **响应格式**: 统一使用Result<T>类型
5. **文档更新**: 接口变更时及时更新文档
6. **测试覆盖**: 为每个接口编写单元测试

## 版本控制

- **当前版本**: v1
- **版本策略**: 向后兼容的更新使用小版本号，破坏性更改使用大版本号
- **废弃策略**: 废弃的接口将在下一个大版本中移除，并提前通知

## 安全考虑

1. **输入验证**: 所有用户输入必须经过严格验证
2. **SQL注入防护**: 使用Prisma ORM防止SQL注入
3. **XSS防护**: 对输出内容进行适当转义
4. **CSRF防护**: 实施CSRF令牌验证
5. **权限控制**: 实施基于角色的访问控制

## 公开接口

### 获取盒型列表（公开）

**接口路径**: `/api/v1/box/getList`
**请求方法**: POST
**权限要求**: 无需认证
**描述**: 获取所有已发布状态的盒型列表，支持分页

#### 请求参数

```typescript
interface PublicBoxListParams {
  page?: number;      // 页码，默认1
  pageSize?: number;  // 每页数量，默认12，最大50
}
```

#### 响应数据

```typescript
interface PublicBoxListResponse {
  list: Array<{
    id: number;
    name: string;
    status: BoxStatus;
    description?: string | null;
    images?: Array<{
      id?: number;
      name: string;
      mimeType?: string;
      sortOrder?: number;
    }>;
  }>;
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}
```

#### 请求示例

```bash
curl -X POST http://localhost:3000/api/v1/box/getList \
  -H "Content-Type: application/json" \
  -d '{
    "page": 1,
    "pageSize": 12
  }'
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "list": [
      {
        "id": 4,
        "name": "天地盖礼盒",
        "status": 1,
        "description": null,
        "images": [
          {
            "id": 123,
            "name": "天地盖礼盒.jpg",
            "mimeType": "image/jpeg",
            "sortOrder": 1
          }
        ]
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 12,
      "total": 2
    }
  },
  "message": "获取盒型列表成功"
}
```

---

**文档版本**: v2.1
**最后更新**: 2025年6月
**维护者**: 开发团队



同时，材料数据库重的纸类数据中的 正度价格和大度价格 应该按照其他参数计算，而不是手动填写
计算逻辑
元/吨
正度价格 = 正度长 * 正度宽 * 克重（g/m²）* 价格
大度同理
注意单位换算

元/平方
正度价格 = 正度长 * 正度宽 *  价格
大度同理
注意单位换算

元/张