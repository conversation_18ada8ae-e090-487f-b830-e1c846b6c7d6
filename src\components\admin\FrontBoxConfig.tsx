'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  message,
  Spin,
  Alert,
  Row,
  Col,
  Empty,
  Avatar,
  Space,
  Typography,
  Badge
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  AppstoreOutlined,
  DragOutlined,
  SettingOutlined
} from '@ant-design/icons';
import {
  DndContext,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  useDroppable,
  useDraggable,
  DragOverlay,
  rectIntersection,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { configApi } from '@/services/configApi';
import { Box } from '@/types/box';
import { FrontBoxConfig as FrontBoxConfigType } from '@/types/config';

const { Text } = Typography;

// 获取盒型缩略图的工具函数
const getBoxThumbnail = (box: Box) => {
  if (box.images && box.images.length > 0) {
    return `/api/v1/admin/box/getImage?id=${box.images[0].id}`;
  }
  return null;
};

// 可拖拽的盒型项目组件（用于可选择区域）
function DraggableBoxItem({ box }: { box: Box }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: `available-${box.id}`,
    data: { box, source: 'available' }
  });

  const style = {
    transform: CSS.Translate.toString(transform),
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="draggable-box-item"
    >
      <div
        style={{
          marginBottom: 8,
          padding: 8,
          border: '1px solid #d9d9d9',
          borderRadius: 8,
          backgroundColor: '#fff',
          cursor: 'grab',
          transition: 'all 0.2s ease',
          overflowX: 'hidden'
        }}
      >
        <Space size="small">
          <DragOutlined style={{ color: '#999', fontSize: 12 }} />
          <Avatar
            shape="square"
            size={32}
            src={getBoxThumbnail(box)}
            icon={<AppstoreOutlined />}
          />
          <div style={{ flex: 1 }}>
            <div style={{
              fontWeight: 500,
              fontSize: 12,
            }}>
              {box.name}
            </div>
          </div>
        </Space>
      </div>
    </div>
  );
}

// 可排序的盒型项目组件（用于已选择区域）
function SortableBoxItem({ box, compact = false }: {
  box: Box;
  compact?: boolean;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: `selected-${box.id}`,
    data: { box, source: 'selected' }
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className="sortable-box-item"
    >
      <div
        style={{
          marginBottom: compact ? 4 : 8,
          padding: compact ? 8 : 12,
          border: '1px solid #d9d9d9',
          borderRadius: 6,
          backgroundColor: isDragging ? '#f0f0f0' : '#fff',
          cursor: 'move',
          overflowX: 'hidden'
        }}
      >
        <Space size="small">
          <DragOutlined style={{ color: '#999', fontSize: compact ? 12 : 14 }} />
          <Avatar
            shape="square"
            size={compact ? 32 : 40}
            src={getBoxThumbnail(box)}
            icon={<AppstoreOutlined />}
          />
          <div style={{ flex: 1 }}>
            <div style={{
              fontWeight: 500,
              fontSize: compact ? 12 : 14,
              lineHeight: compact ? '16px' : '20px'
            }}>
              {box.name}
            </div>
            {!compact && (
              <Text type="secondary" style={{ fontSize: 12 }}>
                ID: {box.id}
              </Text>
            )}
          </div>
        </Space>
      </div>
    </div>
  );
}

// 可放置区域组件
function DroppableArea({
  id,
  children,
  title,
  count,
  maxCount,
  compact = false,
  isOver = false
}: {
  id: string;
  children: React.ReactNode;
  title: string;
  count: number;
  maxCount?: number;
  compact?: boolean;
  isOver?: boolean;
}) {
  const { setNodeRef } = useDroppable({ id });

  return (
    <div
      ref={setNodeRef}
      style={{
        height: '100%',
        border: `2px dashed ${isOver ? '#1890ff' : '#d9d9d9'}`,
        borderRadius: 8,
        backgroundColor: isOver ? '#f0f8ff' : '#fafafa',
        transition: 'all 0.2s ease',
        padding: compact ? 8 : 12,
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <div style={{
        marginBottom: 8,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Text strong style={{ fontSize: compact ? 12 : 14 }}>
          {title} ({count}{maxCount ? `/${maxCount}` : ''})
        </Text>
      </div>
      <div style={{
        flex: 1,
        overflowY: 'auto',
        overflowX: 'hidden',
        minHeight: compact ? 150 : 300,
        height: compact ? 'calc(100% - 40px)' : 'auto'
      }}>
        {children}
      </div>
    </div>
  );
}

interface FrontBoxConfigProps {
  compact?: boolean; // 紧凑模式，适用于仪表盘
}

export default function FrontBoxConfig({ compact = false }: FrontBoxConfigProps) {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [availableBoxes, setAvailableBoxes] = useState<Box[]>([]);
  const [selectedBoxes, setSelectedBoxes] = useState<Box[]>([]);
  const [currentConfig, setCurrentConfig] = useState<FrontBoxConfigType | null>(null);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [overId, setOverId] = useState<string | null>(null);

  // 拖拽传感器设置
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 加载数据
  const loadData = async () => {
    setLoading(true);
    try {
      // 并行加载可用盒型和当前配置
      const [allBoxesResult, configResult] = await Promise.all([
        configApi.getAvailableBoxes(),
        configApi.getFrontBoxConfig()
      ]);

      // 检查API调用是否成功
      if (!allBoxesResult.success) {
        throw new Error(allBoxesResult.error?.message || '获取可用盒型列表失败');
      }
      if (!configResult.success) {
        throw new Error(configResult.error?.message || '获取前端盒型配置失败');
      }

      const allBoxesData = allBoxesResult.data || [];
      const configData = configResult.data || { config: null };

      setCurrentConfig(configData.config);

      // 根据配置设置已选择的盒型
      let selectedBoxesData: Box[] = [];
      if (configData.config && configData.config.boxIds.length > 0) {
        selectedBoxesData = configData.config.boxIds
          .map(id => allBoxesData.find(box => box.id === id))
          .filter(box => box !== undefined) as Box[];
        setSelectedBoxes(selectedBoxesData);
      } else {
        setSelectedBoxes([]);
      }

      // 设置可选择的盒型（排除已选择的）
      const selectedIds = selectedBoxesData.map(box => box.id);
      const availableBoxesData = allBoxesData.filter(box => !selectedIds.includes(box.id));
      setAvailableBoxes(availableBoxesData);
    } catch (error: any) {
      console.error('加载数据失败:', error);
      message.error(error.message || '加载数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadData();
  }, []);

  // 处理拖拽开始
  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  // 处理拖拽悬停
  const handleDragOver = (event: DragOverEvent) => {
    setOverId(event.over?.id as string || null);
  };

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    setActiveId(null);
    setOverId(null);

    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;
    const activeData = active.data.current;
    const overData = over.data.current;

    // 从可选择区域拖拽到已选择区域
    if (activeData?.source === 'available' && overId === 'selected-area') {
      const box = activeData.box;
      if (selectedBoxes.length >= 20) {
        message.warning('最多只能选择20个盒型');
        return;
      }

      // 添加到已选择列表
      setSelectedBoxes(prev => [...prev, box]);
      // 从可选择列表中移除
      setAvailableBoxes(prev => prev.filter(item => item.id !== box.id));
      return;
    }

    // 从已选择区域拖拽到可选择区域
    if (activeData?.source === 'selected' && overId === 'available-area') {
      const box = activeData.box;

      // 从已选择列表中移除
      setSelectedBoxes(prev => prev.filter(item => item.id !== box.id));
      // 添加到可选择列表
      setAvailableBoxes(prev => [...prev, box]);
      return;
    }

    // 在已选择区域内排序
    if (activeData?.source === 'selected' && overData?.source === 'selected') {
      const activeIndex = selectedBoxes.findIndex(item => `selected-${item.id}` === activeId);
      const overIndex = selectedBoxes.findIndex(item => `selected-${item.id}` === overId);

      if (activeIndex !== overIndex) {
        setSelectedBoxes(items => arrayMove(items, activeIndex, overIndex));
      }
    }
  };

  // 保存配置
  const handleSave = async () => {
    setSaving(true);
    try {
      const boxIds = selectedBoxes.map(box => box.id);
      const updateResult = await configApi.updateFrontBoxConfig({ boxIds });

      if (!updateResult.success) {
        throw new Error(updateResult.error?.message || '更新前端盒型配置失败');
      }

      message.success('保存成功');

      // 重新加载配置
      const configResult = await configApi.getFrontBoxConfig();
      if (configResult.success && configResult.data) {
        setCurrentConfig(configResult.data.config);
      }
    } catch (error: any) {
      console.error('保存失败:', error);
      message.error(error.message || '保存失败，请稍后重试');
    } finally {
      setSaving(false);
    }
  };

  // 检查是否有未保存的更改
  const hasUnsavedChanges = () => {
    const currentBoxIds = selectedBoxes.map(box => box.id);
    const savedBoxIds = currentConfig?.boxIds || [];

    if (currentBoxIds.length !== savedBoxIds.length) {
      return true;
    }

    return !currentBoxIds.every((id, index) => id === savedBoxIds[index]);
  };

  const cardTitle = (
    <Space>
      <SettingOutlined />
      前端盒型配置
      <Badge count={selectedBoxes.length} style={{ backgroundColor: '#52c41a' }} />
    </Space>
  );

  const cardExtra = (
    <Space size="small">
      <Button
        type="primary"
        size="small"
        icon={<SaveOutlined />}
        loading={saving}
        disabled={!hasUnsavedChanges()}
        onClick={handleSave}
      >
        保存
      </Button>
      <Button
        size="small"
        icon={<ReloadOutlined />}
        loading={loading}
        onClick={loadData}
      />
    </Space>
  );

  // 获取当前拖拽的盒型
  const activeBox = activeId ?
    (activeId.startsWith('available-') ?
      availableBoxes.find(box => `available-${box.id}` === activeId) :
      selectedBoxes.find(box => `selected-${box.id}` === activeId)
    ) : null;

  return (
    <Card
      title={cardTitle}
      extra={cardExtra}
      style={{
        height: compact ? '400' : 'auto',
        borderRadius: 8,
        boxShadow: compact ? '0 2px 8px rgba(0,0,0,0.06)' : 'none'
      }}
      styles={{
        body: {
          padding: compact ? 12 : 24,
          height: compact ? 'calc(100% - 57px)' : 'auto',
          overflow: 'hidden'
        }
      }}
    >
      <Spin spinning={loading}>
        <DndContext
          sensors={sensors}
          collisionDetection={rectIntersection}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragEnd={handleDragEnd}
        >
          <Row gutter={[16, 16]} style={{ height: compact ? '100%' : 'auto' }}>
            {/* 左侧：可选择的盒型 */}
            <Col
              xs={24}
              sm={24}
              md={12}
              style={{
                height: compact ? 'calc(100% - 8px)' : 'auto',
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <DroppableArea
                id="available-area"
                title="可选择的盒型"
                count={availableBoxes.length}
                compact={compact}
                isOver={overId === 'available-area'}
              >
                {availableBoxes.length === 0 ? (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="暂无可用盒型"
                    style={{ margin: compact ? '20px 0' : '40px 0' }}
                  />
                ) : (
                  availableBoxes.map((box) => (
                    <DraggableBoxItem
                      key={box.id}
                      box={box}
                    />
                  ))
                )}
              </DroppableArea>
            </Col>

            {/* 右侧：已选择的盒型 */}
            <Col
              xs={24}
              sm={24}
              md={12}
              style={{
                height: compact ? 'calc(100% - 8px)' : 'auto',
                display: 'flex',
                flexDirection: 'column'
              }}
            >
              <DroppableArea
                id="selected-area"
                title="已选择的盒型"
                count={selectedBoxes.length}
                maxCount={20}
                compact={compact}
                isOver={overId === 'selected-area'}
              >
                {selectedBoxes.length === 0 ? (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={compact ? "拖拽盒型到此处" : "拖拽左侧盒型到此处"}
                    style={{ margin: compact ? '20px 0' : '40px 0' }}
                  />
                ) : (
                  <SortableContext
                    items={selectedBoxes.map(box => `selected-${box.id}`)}
                    strategy={verticalListSortingStrategy}
                  >
                    {selectedBoxes.map((box) => (
                      <SortableBoxItem
                        key={box.id}
                        box={box}
                        compact={compact}
                      />
                    ))}
                  </SortableContext>
                )}
              </DroppableArea>
            </Col>
          </Row>

          {/* 拖拽预览 */}
          <DragOverlay>
            {activeBox ? (
              <div style={{
                padding: compact ? 8 : 12,
                border: '1px solid #1890ff',
                borderRadius: 6,
                backgroundColor: '#fff',
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                transform: 'rotate(5deg)',
                overflowX: 'hidden'
              }}>
                <Space size="small">
                  <DragOutlined style={{ color: '#1890ff', fontSize: compact ? 12 : 14 }} />
                  <Avatar
                    shape="square"
                    size={compact ? 32 : 40}
                    src={getBoxThumbnail(activeBox)}
                    icon={<AppstoreOutlined />}
                  />
                  <div>
                    <div style={{
                      fontWeight: 500,
                      fontSize: compact ? 12 : 14,
                      color: '#1890ff'
                    }}>
                      {activeBox.name}
                    </div>
                    {!compact && (
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        ID: {activeBox.id}
                      </Text>
                    )}
                  </div>
                </Space>
              </div>
            ) : null}
          </DragOverlay>
        </DndContext>
      </Spin>
    </Card>
  );
}
