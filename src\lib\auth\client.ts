/**
 * 客户端权限检查工具函数
 * 这些函数可以在客户端组件中安全使用
 */

import { UserRole } from '@/types/user';

/**
 * 权限检查函数
 */
export function hasPermission(userRole: UserRole, requiredRoles: UserRole[]): boolean {
  return requiredRoles.includes(userRole);
}

/**
 * 管理员权限检查
 */
export function isAdmin(userRole: UserRole): boolean {
  return userRole === UserRole.ADMIN;
}

/**
 * 内部用户权限检查（包括管理员和内部用户）
 */
export function isInternalUser(userRole: UserRole): boolean {
  return [UserRole.ADMIN, UserRole.INTERNAL_USER].includes(userRole);
}

/**
 * 超级用户权限检查
 */
export function isSuperUser(userRole: UserRole): boolean {
  return userRole === UserRole.SUPER_USER;
}

/**
 * 检查用户是否有访问管理后台的权限
 */
export function canAccessAdmin(userRole: UserRole): boolean {
  return [UserRole.ADMIN, UserRole.INTERNAL_USER].includes(userRole);
}

/**
 * 获取用户角色的中文标签
 */
export function getUserRoleLabel(role: UserRole): string {
  const roleLabels: Record<UserRole, string> = {
    [UserRole.USER]: '普通用户',
    [UserRole.SUPER_USER]: '超级用户',
    [UserRole.INTERNAL_USER]: '内部用户',
    [UserRole.ADMIN]: '管理员'
  };
  return roleLabels[role] || '未知角色';
}

/**
 * 获取用户角色对应的颜色
 */
export function getUserRoleColor(role: UserRole): string {
  const roleColors: Record<UserRole, string> = {
    [UserRole.USER]: 'default',
    [UserRole.SUPER_USER]: 'orange',
    [UserRole.INTERNAL_USER]: 'blue',
    [UserRole.ADMIN]: 'red'
  };
  return roleColors[role] || 'default';
}

/**
 * 检查用户是否有特定权限
 */
export function checkUserPermissions(userRole: UserRole) {
  return {
    canAccessAdmin: canAccessAdmin(userRole),
    isAdmin: isAdmin(userRole),
    isInternalUser: isInternalUser(userRole),
    isSuperUser: isSuperUser(userRole),
    roleLabel: getUserRoleLabel(userRole),
    roleColor: getUserRoleColor(userRole)
  };
}
