'use client';

import React from 'react';
import { Result, Button, Typography, Space } from 'antd';
import { LockOutlined, HomeOutlined, MailOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { UserRole } from '@/types/user';
import { getUserRoleLabel } from '@/lib/auth/client';

const { Paragraph, Text } = Typography;

interface AccessDeniedProps {
  userRole?: UserRole;
  message?: string;
  showContactInfo?: boolean;
}

/**
 * 权限不足提示页面组件
 */
export default function AccessDenied({ 
  userRole, 
  message,
  showContactInfo = true 
}: AccessDeniedProps) {
  const router = useRouter();



  const getDefaultMessage = (role?: UserRole): string => {
    if (!role) {
      return '您需要登录并具有管理员权限才能访问此页面。';
    }

    switch (role) {
      case UserRole.USER:
        return '普通用户无法访问管理后台，如需管理权限请联系系统管理员。';
      case UserRole.SUPER_USER:
        return '超级用户权限不足以访问管理后台，如需管理权限请联系系统管理员。';
      case UserRole.INTERNAL_USER:
      case UserRole.ADMIN:
        return '您已具有管理权限，但可能遇到了系统错误，请刷新页面或联系技术支持。';
      default:
        return '权限验证失败，请联系系统管理员。';
    }
  };

  const handleGoHome = () => {
    router.push('/');
  };

  const handleContactAdmin = () => {
    // 这里可以根据实际需求修改联系方式
    window.location.href = 'mailto:<EMAIL>?subject=管理后台权限申请';
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      padding: '20px',
      background: '#f5f5f5'
    }}>
      <div style={{ 
        maxWidth: '600px', 
        width: '100%',
        background: '#fff',
        borderRadius: '8px',
        padding: '40px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
      }}>
        <Result
          status="403"
          title="访问受限"
          icon={<LockOutlined style={{ color: '#ff4d4f', fontSize: '72px' }} />}
          subTitle={
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <Paragraph style={{ fontSize: '16px', margin: 0 }}>
                {message || getDefaultMessage(userRole)}
              </Paragraph>
              
              {userRole && (
                <Paragraph style={{ margin: 0 }}>
                  <Text type="secondary">当前角色：</Text>
                  <Text strong>{getUserRoleLabel(userRole)}</Text>
                </Paragraph>
              )}
              
              <Paragraph style={{ margin: 0, fontSize: '14px' }} type="secondary">
                管理后台仅限管理员和内部用户访问。如果您认为这是一个错误，请联系系统管理员。
              </Paragraph>
            </Space>
          }
          extra={
            <Space size="middle" wrap>
              <Button 
                type="primary" 
                icon={<HomeOutlined />}
                onClick={handleGoHome}
                size="large"
              >
                返回首页
              </Button>
              
              {userRole === UserRole.INTERNAL_USER || userRole === UserRole.ADMIN ? (
                <Button 
                  onClick={handleRefresh}
                  size="large"
                >
                  刷新页面
                </Button>
              ) : (
                showContactInfo && (
                  <Button 
                    icon={<MailOutlined />}
                    onClick={handleContactAdmin}
                    size="large"
                  >
                    申请权限
                  </Button>
                )
              )}
            </Space>
          }
        />
        
        {showContactInfo && (
          <div style={{ 
            marginTop: '40px', 
            padding: '20px', 
            background: '#fafafa', 
            borderRadius: '6px',
            textAlign: 'center'
          }}>
            <Typography.Title level={5} style={{ margin: '0 0 12px 0' }}>
              需要帮助？
            </Typography.Title>
            <Paragraph style={{ margin: 0, fontSize: '14px' }} type="secondary">
              如果您需要管理后台访问权限，请联系系统管理员：
              <br />
              邮箱：<EMAIL>
              <br />
              电话：400-000-0000
            </Paragraph>
          </div>
        )}
      </div>
    </div>
  );
}
