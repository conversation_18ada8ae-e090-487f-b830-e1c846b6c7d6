'use client';

import React, { useCallback, useState, useEffect } from 'react';
import { Card, Row, Col, Button, message } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { perfLog } from '@/lib/utils/perfLog';
import { useCalculationState } from '../hooks/useCalculationState';
import { useStepNavigation, STEP_ORDER } from '../hooks/useStepNavigation';
import { calculationEngine } from '../utils/calculationEngine';
import { configApi } from '@/services/configApi';
import { ProfitTaxConfig } from '@/types/config';
import { CalculationStep } from '../types/calculation';
import { Box } from '@/types/box';

import StepIndicator from './StepIndicator';
import CalculationSummary from './CalculationSummary';

// 步骤组件
import BasicInfoStep from './steps/BasicInfoStep';
import PackagingStep from './steps/PackagingStep';
import ProcessStep from './steps/ProcessStep';
import AccessoryStep from './steps/AccessoryStep';
import ProcessingFeeStep from './steps/ProcessingFeeStep';
import QuotationStep from './steps/QuotationStep';


interface BoxCalculationWizardProps {
  sourceBoxId?: number;
  sourceBox?: Box | null;
  pageLoading?: boolean;
}

/**
 * 盒型计算向导主组件
 */
const BoxCalculationWizard: React.FC<BoxCalculationWizardProps> = ({ sourceBoxId, sourceBox, pageLoading = false }) => {
  const {
    state,
    updateBasicInfo,
    updatePartConfig,
    updatePackagingConfig,
    updateMaterialConfig,
    updateProcessConfig,
    updateAccessoryConfig,
    updateProcessingFeeConfig,
    updateFormulaConfig,
    updateQuotation,
    setCurrentStep,
    setIsCalculating,
    resetState,
    getStepValidation
  } = useCalculationState();

  const stepNavigation = useStepNavigation({
    currentStep: state.currentStep,
    onStepChange: setCurrentStep,
    getStepValidation
  });

  // 利润税率配置状态
  const [profitTaxConfig, setProfitTaxConfig] = useState<ProfitTaxConfig>({
    profitRate: 0.2,  // 默认20%
    taxRate: 0.06     // 默认6%
  });

  // 获取利润税率配置
  const fetchProfitTaxConfig = useCallback(async () => {
    try {
      const result = await configApi.getProfitTaxConfig();
      if (result.success && result.data?.config) {
        setProfitTaxConfig(result.data.config);
        perfLog.debug('BoxCalculationWizard获取利润税率配置成功:', result.data.config);
      }
    } catch (error) {
      perfLog.error('BoxCalculationWizard获取利润税率配置失败:', error);
      // 使用默认配置，不显示错误信息
    }
  }, []);

  // 组件初始化时获取利润税率配置
  useEffect(() => {
    fetchProfitTaxConfig();
  }, [fetchProfitTaxConfig]);

  // 自动重新计算报价 - 使用useCallback确保函数稳定
  const recalculateQuotation = useCallback(async () => {
    try {
      setIsCalculating(true);
      const quotation = await calculationEngine.generateQuotation(state, profitTaxConfig);
      updateQuotation(quotation);
      perfLog.debug('报价计算完成:', quotation);
    } catch (error) {
      perfLog.error('计算错误:', error);
      message.error('计算失败，请检查配置');
    } finally {
      setIsCalculating(false);
    }
  }, [state, profitTaxConfig, setIsCalculating, updateQuotation]);

  // 重置所有数据
  const handleReset = () => {
    resetState();
    message.info('已重置所有数据');
  };

  // 渲染当前步骤的内容
  const renderStepContent = () => {
    const commonProps = {
      state,
      onUpdate: {
        basicInfo: updateBasicInfo,
        partConfig: updatePartConfig,
        packagingConfig: updatePackagingConfig,
        materialConfig: updateMaterialConfig,
        processConfig: updateProcessConfig,
        accessoryConfig: updateAccessoryConfig,
        processingFeeConfig: updateProcessingFeeConfig,
        formulaConfig: updateFormulaConfig,
        quotation: updateQuotation
      },
      onRecalculate: recalculateQuotation
    };

    switch (state.currentStep) {
      case CalculationStep.BASIC_INFO:
        return <BasicInfoStep {...commonProps} sourceBoxId={sourceBoxId} sourceBox={sourceBox} pageLoading={pageLoading} />;
      case CalculationStep.PACKAGING:
        return <PackagingStep {...commonProps} />;
      case CalculationStep.PROCESS:
        return <ProcessStep {...commonProps} />;
      case CalculationStep.ACCESSORY:
        return <AccessoryStep {...commonProps} />;
      case CalculationStep.PROCESSING_FEE:
        return <ProcessingFeeStep {...commonProps} />;
      case CalculationStep.QUOTATION:
        return <QuotationStep {...commonProps} />;
      default:
        return <div>未知步骤</div>;
    }
  };


  return (
    <div style={{ marginTop: 24 }}>
      <Row gutter={16}>
        <Col span={24}>
          <Card style={{ marginBottom: 16 }}>
            {/* 步骤指示器 */}
            <StepIndicator
              steps={STEP_ORDER}
              currentStep={state.currentStep}
              onStepClick={stepNavigation.goToStep}
              getStepValidation={getStepValidation}
            />
          </Card>
        </Col>
        {/* 主要内容区域 */}
        <Col span={state.currentStep === CalculationStep.QUOTATION ? 24 : 18}>
          <Card
            title={`步骤 ${STEP_ORDER.indexOf(state.currentStep) + 1}: ${getStepTitle(state.currentStep)}`}
            loading={state.isCalculating}
            className="min-h-96"
          >
            {/* 步骤内容 */}
            {renderStepContent()}
          </Card>
        </Col>

        {/* 侧边栏 - 费用汇总 */}
        {state.currentStep !== CalculationStep.QUOTATION && (
          <Col span={6} >
            <CalculationSummary
              quotation={state.quotation}
              basicInfo={state.basicInfo}
              isCalculating={state.isCalculating}
              onRecalculate={recalculateQuotation}
              hasCalculatedImposition={state.packagingConfig.hasCalculatedImposition}
              materialCostDetails={state.packagingConfig.materialCostDetails || []}
            />

            {/* 导航按钮 - 还原到费用汇总下方 */}
            <Row justify="center" style={{ marginTop: 24 }}>
              <Button onClick={stepNavigation.previousStep} disabled={!stepNavigation.canGoPrevious} > 上一步 </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset} > 重置 </Button>
              <Button
                type="primary"
                onClick={stepNavigation.nextStep}
                disabled={!stepNavigation.canGoNext}
              >
                下一步
              </Button>
            </Row>
          </Col>
        )}
      </Row>
    </div>
  );
};

// 获取步骤标题
function getStepTitle(step: CalculationStep): string {
  const stepTitles = {
    [CalculationStep.BASIC_INFO]: '基础信息',
    [CalculationStep.PACKAGING]: '部件合并与材料选择',
    [CalculationStep.PROCESS]: '工艺选择',
    [CalculationStep.ACCESSORY]: '配件选择',
    [CalculationStep.PROCESSING_FEE]: '加工选择',
    [CalculationStep.QUOTATION]: '确认报价'
  };
  return stepTitles[step] || '未知步骤';
}

export default BoxCalculationWizard; 
