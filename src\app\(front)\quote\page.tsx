'use client';

import React, { useState, useEffect } from 'react';
import { Row, Col, Pagination, Spin, Alert, Empty, ConfigProvider, Layout } from 'antd';
import CustomBoxCard from '../components/CustomBoxCard';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { PublicBoxListParams } from '@/types/box';
import { boxApi } from '@/services/boxApi';

interface BoxItem {
  id: number;
  name: string;
  status: number;
  description?: string | null;
  images?: Array<{
    id?: number;
    name: string;
    mimeType?: string;
    sortOrder?: number;
  }>;
}

export default function QuotePage() {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [boxes, setBoxes] = useState<BoxItem[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 12,
    total: 0,
  });

  // 获取盒型列表数据
  const fetchBoxList = async (params: PublicBoxListParams = {}) => {
    try {
      setLoading(true);
      setError(null);

      const { page = 1, pageSize = 12 } = params;
      const result = await boxApi.getPublicBoxList({ page, pageSize });

      if (result.success && result.data) {
        setBoxes(result.data.list);
        setPagination({
          current: result.data.pagination.page,
          pageSize: result.data.pagination.pageSize,
          total: result.data.pagination.total,
        });
      } else {
        setError(result.error?.message || '获取盒型列表失败');
      }
    } catch (err) {
      console.error('获取盒型列表失败:', err);
      setError('网络错误，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载数据
  useEffect(() => {
    fetchBoxList();
  }, []);

  // 处理分页变化
  const handlePageChange = (page: number, pageSize?: number) => {
    fetchBoxList({ page, pageSize: pageSize || pagination.pageSize });
  };

  // 获取图片URL
  const getImageUrl = (imageId?: number) => {
    if (!imageId) return undefined;
    return `/api/v1/box/getImage?id=${imageId}`;
  };

  // 处理盒型卡片点击
  const handleBoxClick = (box: BoxItem) => {
    // TODO: 跳转到盒型详情或计算页面
    console.log('点击盒型:', box);
  };

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#FF422D',
          colorPrimaryHover: '#FF6655',
          colorPrimaryActive: '#CC3B26',
        },
      }}
    >
      <Layout style={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
        <Header />
        <div style={{
          flex: 1,
          padding: '24px 0'
        }}>
        <div style={{ 
          maxWidth: '1200px', 
          margin: '0 auto', 
          padding: '0 20px' 
        }}>
          {/* 页面标题 */}
          <div style={{ 
            textAlign: 'center', 
            marginBottom: '32px' 
          }}>
            <h1 style={{ 
              fontSize: '32px', 
              fontWeight: 'bold', 
              color: '#333',
              margin: 0
            }}>
              包装报价
            </h1>
            <p style={{ 
              fontSize: '16px', 
              color: '#666', 
              marginTop: '8px',
              margin: 0
            }}>
              选择您需要的盒型，获取专业报价
            </p>
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert
              message="加载失败"
              description={error}
              type="error"
              showIcon
              style={{ marginBottom: '24px' }}
              action={
                <button
                  onClick={() => fetchBoxList()}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: '#FF422D',
                    cursor: 'pointer',
                    textDecoration: 'underline'
                  }}
                >
                  重试
                </button>
              }
            />
          )}

          {/* 加载状态 */}
          {loading && (
            <div style={{ 
              textAlign: 'center', 
              padding: '60px 0' 
            }}>
              <Spin size="large" />
              <p style={{ 
                marginTop: '16px', 
                color: '#666' 
              }}>
                正在加载盒型数据...
              </p>
            </div>
          )}

          {/* 空数据状态 */}
          {!loading && !error && boxes.length === 0 && (
            <div style={{ 
              textAlign: 'center', 
              padding: '60px 0' 
            }}>
              <Empty
                description="暂无可用的盒型产品"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            </div>
          )}

          {/* 盒型列表 */}
          {!loading && !error && boxes.length > 0 && (
            <>
              <Row gutter={[24, 24]}>
                {boxes.map((box) => {
                  const firstImage = box.images?.[0];
                  const imageUrl = firstImage ? getImageUrl(firstImage.id) : undefined;
                  
                  return (
                    <Col 
                      key={box.id}
                      xs={24}
                      sm={12}
                      md={8}
                      lg={6}
                      xl={6}
                    >
                      <CustomBoxCard
                        title={box.name}
                        subtitle={box.description || undefined}
                        imageUrl={imageUrl}
                        imageAlt={box.name}
                        onClick={() => handleBoxClick(box)}
                        hoverable={true}
                        width="100%"
                        height="auto"
                        imageHeight={180}
                      />
                    </Col>
                  );
                })}
              </Row>

              {/* 分页控件 */}
              {pagination.total > pagination.pageSize && (
                <div style={{ 
                  textAlign: 'center', 
                  marginTop: '48px' 
                }}>
                  <Pagination
                    current={pagination.current}
                    pageSize={pagination.pageSize}
                    total={pagination.total}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total, range) => 
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                    }
                    pageSizeOptions={['12', '24', '36', '48']}
                    onChange={handlePageChange}
                    onShowSizeChange={handlePageChange}
                  />
                </div>
              )}
            </>
          )}
        </div>
        </div>
        <Footer />
      </Layout>
    </ConfigProvider>
  );
}
