import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withValidation } from '@/lib/middleware/errorHandler';
import { paginatedResponse } from '@/lib/utils/apiResponse';
import { publicBoxListSchema } from '@/lib/validations/config';
import { PublicBoxListParams } from '@/types/box';
import { BoxStatus } from '@/types/box';
import { z } from 'zod';

type GetListParams = z.infer<typeof publicBoxListSchema>;

const handler = withValidation<GetListParams>(
  publicBoxListSchema,
  async (request: NextRequest, validatedData: GetListParams) => {
    try {
      const { page = 1, pageSize = 12 } = validatedData;
      const skip = (page - 1) * pageSize;

      // 构建查询条件 - 只返回已发布的盒型
      const where = {
        status: BoxStatus.PUBLISHED,
        isDel: false,
      };

      // 查询总数和列表数据
      const [total, list] = await Promise.all([
        prisma.box.count({ where }),
        prisma.box.findMany({
          where,
          select: {
            id: true,
            name: true,
            status: true,
            description: true,
            images: {
              select: {
                id: true,
                name: true,
                mimeType: true,
                sortOrder: true,
              },
              orderBy: {
                sortOrder: 'asc',
              },
            },
          },
          skip,
          take: pageSize,
          orderBy: { createdAt: 'desc' },
        }),
      ]);

      return paginatedResponse(
        list,
        {
          page,
          pageSize,
          total,
        },
        '获取盒型列表成功'
      );
    } catch (error) {
      console.error('获取盒型列表失败:', error);
      throw error;
    }
  }
);

export const POST = handler;
