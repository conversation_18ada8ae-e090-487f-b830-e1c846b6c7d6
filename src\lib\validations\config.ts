import { z } from 'zod';

// 前端盒型配置校验
export const frontBoxConfigSchema = z.object({
  boxIds: z.array(z.number().int().positive('盒型ID必须是正整数'))
    .min(0, '参数错误')
    .max(20, '最多只能选择20个盒型')
});

// 获取前端盒型数据的参数校验（空参数）
export const getFrontBoxSchema = z.object({});

// 更新前端盒型配置的参数校验
export const updateFrontBoxConfigSchema = frontBoxConfigSchema;

// 配置列表查询参数校验
export const configListSchema = z.object({
  page: z.number().int().min(1, '页码必须大于0').optional().default(1),
  pageSize: z.number().int().min(1, '每页数量必须大于0').max(100, '每页数量不能超过100').optional().default(10),
  name: z.string().optional()
});

// 公开盒型列表查询参数校验
export const publicBoxListSchema = z.object({
  page: z.number().int().min(1, '页码必须大于0').optional().default(1),
  pageSize: z.number().int().min(1, '每页数量必须大于0').max(50, '每页数量不能超过50').optional().default(12),
});

// 创建配置参数校验
export const createConfigSchema = z.object({
  name: z.string()
    .min(1, '配置名称不能为空')
    .max(100, '配置名称长度不能超过100')
    .regex(/^[a-zA-Z][a-zA-Z0-9_]*$/, '配置名称只能包含字母、数字和下划线，且必须以字母开头'),
  value: z.any() // JSON 值，可以是任意类型
});

// 更新配置参数校验
export const updateConfigSchema = z.object({
  id: z.number().int().positive('配置ID必须是正整数'),
  name: z.string()
    .min(1, '配置名称不能为空')
    .max(100, '配置名称长度不能超过100')
    .regex(/^[a-zA-Z][a-zA-Z0-9_]*$/, '配置名称只能包含字母、数字和下划线，且必须以字母开头')
    .optional(),
  value: z.any().optional() // JSON 值，可以是任意类型
});

// 获取配置详情参数校验
export const getConfigDetailSchema = z.object({
  id: z.number().int().positive('配置ID必须是正整数').optional(),
  name: z.string().min(1, '配置名称不能为空').optional()
}).refine(
  (data) => data.id !== undefined || data.name !== undefined,
  {
    message: '必须提供配置ID或配置名称中的一个',
    path: ['id', 'name']
  }
);

// 删除配置参数校验
export const deleteConfigSchema = z.object({
  id: z.number().int().positive('配置ID必须是正整数')
});

// 导出类型
export type FrontBoxConfigParams = z.infer<typeof frontBoxConfigSchema>;
export type GetFrontBoxParams = z.infer<typeof getFrontBoxSchema>;
export type UpdateFrontBoxConfigParams = z.infer<typeof updateFrontBoxConfigSchema>;
export type ConfigListParams = z.infer<typeof configListSchema>;
export type CreateConfigParams = z.infer<typeof createConfigSchema>;
export type UpdateConfigParams = z.infer<typeof updateConfigSchema>;
export type GetConfigDetailParams = z.infer<typeof getConfigDetailSchema>;
export type DeleteConfigParams = z.infer<typeof deleteConfigSchema>;
