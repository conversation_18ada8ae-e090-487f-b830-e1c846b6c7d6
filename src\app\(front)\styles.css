/* 前台页面专用样式 - 使用 Ant Design 组件的传统电商风格 */

/* 全局样式重置 */
.front-layout {
  min-height: 100vh;
  background: #ccc;
  font-family: "Microsoft YaHei", Arial, sans-serif;
}

/* 轮播图样式 */
/* .ant-carousel .slick-dots {
  bottom: 10px;
} */

.ant-carousel .slick-dots li button {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
}

.ant-carousel .slick-dots li.slick-active button {
  background: #1890ff;
}

/* Ant Design 组件样式调整 */
.ant-card {
  border-radius: 0 !important;
}

.ant-card-head {
  border-radius: 0 !important;
}

.ant-card-body {
  border-radius: 0 !important;
}

.ant-btn {
  border-radius: 3px !important;
}

.ant-input {
  border-radius: 3px !important;
}

/* 分类菜单样式 */
.category-menu .ant-menu-item {
  padding-left: 15px !important;
  height: 32px !important;
  line-height: 32px !important;
  border-bottom: 1px solid #f0f0f0;
}

.category-menu .ant-menu-item:hover {
  background-color: #f5f5f5 !important;
}

.category-menu .ant-menu-item a {
  color: #666 !important;
}

.category-menu .ant-menu-item:hover a {
  color: #1890ff !important;
}

/* 页脚链接样式 */
.ant-typography a {
  color: inherit;
  text-decoration: none;
}

.ant-typography a:hover {
  color: #fff !important;
}

/* 传统电商网站样式 */
.traditional-layout {
  max-width: 1200px;
  margin: 0 auto;
  background: #fff;
}

/* 简单的悬浮效果 */
.simple-hover {
  transition: background-color 0.2s ease;
}

.simple-hover:hover {
  background-color: #f0f8ff;
}

/* 传统边框样式 */
.traditional-border {
  border: 1px solid #e0e0e0;
}

/* 传统按钮样式 */
.traditional-button {
  background: #1890ff;
  color: #fff;
  border: none;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 12px;
}

.traditional-button:hover {
  background: #40a9ff;
}


/* 基础文字样式 */
.text-small {
  font-size: 12px;
}

.text-normal {
  font-size: 13px;
}

.text-large {
  font-size: 14px;
}

/* 基础颜色 */
.text-primary {
  color: #1890ff;
}

.text-secondary {
  color: #666;
}

.text-muted {
  color: #999;
}

/* 基础间距 */
.margin-small {
  margin: 5px;
}

.padding-small {
  padding: 5px;
}

.margin-normal {
  margin: 10px;
}

.padding-normal {
  padding: 10px;
}

/* 简化的导航栏样式 */
.front-header {
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
}

/* 简化的页脚样式 */
.front-footer {
  background: #333;
  color: #ccc;
}

/* 响应式优化 - 保持基本的移动端适配 */
@media (max-width: 768px) {
  .traditional-layout {
    padding: 0 10px;
  }

  /* 移动端隐藏侧边栏，改为垂直布局 */
  .mobile-stack {
    flex-direction: column;
  }

  .mobile-full-width {
    width: 100%;
  }
}

/* CustomCard 组件样式 */
.custom-card {
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.custom-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.custom-card .ant-card-cover img {
  transition: transform 0.3s ease;
}

.custom-card:hover .ant-card-cover img {
  transform: scale(1.05);
}

.custom-card .ant-card-body {
  padding: 12px 16px;
}

/* 产品卡片网格布局 */
.product-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .product-card-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
    padding: 15px 0;
  }

  .custom-card {
    width: 100% !important;
  }
}

@media (max-width: 480px) {
  .product-card-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    padding: 10px 0;
  }
}

/* 盒型展示区域样式 */
.box-showcase {
  background: #fff;
  margin: 20px 0;
}

.box-showcase .ant-row {
  justify-content: center;
}

.box-showcase .custom-card {
  margin: 0 auto;
  border-radius: 8px !important;
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.box-showcase .custom-card:hover {
  border-color: #FF422D;
  box-shadow: 0 4px 20px rgba(255, 66, 45, 0.15);
}

.box-showcase .custom-card .ant-card-cover {
  border-radius: 8px 8px 0 0 !important;
}

.box-showcase .custom-card .ant-card-body {
  border-radius: 0 0 8px 8px !important;
}

/* 盒型展示标题样式 */
.box-showcase h2 {
  color: #333;
  font-weight: 600;
  margin-bottom: 8px;
}

.box-showcase p {
  color: #666;
  font-size: 16px;
  line-height: 1.5;
}

/* 空状态和错误状态样式 */
.box-showcase .ant-empty {
  padding: 40px 0;
}

.box-showcase .ant-alert {
  border-radius: 8px;
}

/* 加载状态样式 */
.box-showcase .ant-spin {
  display: block;
  text-align: center;
}

/* 基础滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
}

::-webkit-scrollbar-thumb:hover {
  background: #999;
}
